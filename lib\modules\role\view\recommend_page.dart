import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:rolio/common/constants/colors_constants.dart';
import 'package:rolio/common/constants/message_constants.dart';
import 'package:rolio/common/constants/string_constants.dart';
import 'package:rolio/modules/role/controller/recommend_controller.dart';
import 'package:rolio/modules/role/view/ai_role_card.dart';
import 'package:rolio/widgets/skeleton/role_card_skeleton.dart';
import 'package:rolio/widgets/skeleton/role_grid_skeleton.dart';
import 'package:rolio/widgets/skeleton/loading_more_skeleton.dart';

/// 推荐页面
/// 
/// 展示推荐的AI角色列表，支持下拉刷新和点击进入聊天
class RecommendPage extends GetView<RecommendController> with WidgetsBindingObserver {
  const RecommendPage({Key? key}) : super(key: key);

  // 跟踪Observer，避免重复添加 - 修复内存泄漏
  static _PageLifecycleObserver? _observer;

  @override
  Widget build(BuildContext context) {
    // 确保页面显示时立即加载推荐角色
    WidgetsBinding.instance.addPostFrameCallback((_) {
      controller.loadRecommendedRoles(forceRefresh: false);

      // 添加应用生命周期监听 - 修复内存泄漏
      if (_observer == null) {
        _observer = _PageLifecycleObserver(controller);
        WidgetsBinding.instance.addObserver(_observer!);
      }
    });
    
    // 设置系统UI样式
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.light,
        systemNavigationBarColor: Colors.black,
        systemNavigationBarIconBrightness: Brightness.light,
      ),
    );
    
    return Scaffold(
      backgroundColor: Colors.black,
      body: _buildBody(context),
    );
  }

  /// 构建页面主体内容
  Widget _buildBody(BuildContext context) {
    return Obx(() {
      final isLoading = controller.isLoading.value;
      final showSkeleton = controller.showSkeleton.value;
      final roles = controller.getRoles();
      
      // 如果是首次加载或强制刷新，且需要显示骨架屏
      if (showSkeleton && !controller.isLoadingMore.value) {
        return const RoleGridSkeleton();
      }
      
      // 处理不同状态的UI展示
      if (isLoading && roles.isEmpty && !controller.isLoadingMore.value) {
        return _buildLoadingView();
      }
      
      if (roles.isEmpty) {
        return _buildEmptyView();
      }
      
      // 已有数据，显示角色网格
      return _buildRolesGridView(roles);
    });
  }
  
  /// 构建加载中视图
  Widget _buildLoadingView() {
    // 使用新的骨架屏组件替代原来的加载指示器
    return const RoleGridSkeleton();
  }
  
  /// 构建空数据视图
  Widget _buildEmptyView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.psychology_alt, // 使用更符合AI助手的图标
            size: 76,
            color: Colors.white.withOpacity(0.6),
          ),
          const SizedBox(height: 24),
          Text(
            MessageConstants.noRecommendedRolesMessage, 
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.white.withOpacity(0.8),
              fontSize: 18,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 32),
          ElevatedButton(
            onPressed: () => controller.loadRecommendedRoles(forceRefresh: true),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 28, vertical: 14),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
              elevation: 4,
            ),
            child: Text(
              MessageConstants.retryLoadingMessage, 
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  /// 构建角色网格视图
  Widget _buildRolesGridView(List<dynamic> roles) {
    return RefreshIndicator(
      onRefresh: () => controller.refreshRecommendedRoles(),
      color: AppColors.primary,
      backgroundColor: Colors.black.withOpacity(0.8),
      displacement: 40,
      edgeOffset: 20,
      strokeWidth: 3,
      child: NotificationListener<ScrollNotification>(
        onNotification: controller.handleScrollNotification,
        child: CustomScrollView(
          physics: const BouncingScrollPhysics(
            parent: AlwaysScrollableScrollPhysics(),
          ),
          // 使用键值保持滚动位置
          key: const PageStorageKey('recommend_grid'),
          slivers: [
            // 添加顶部间距
            const SliverToBoxAdapter(
              child: SizedBox(height: 8),
            ),
            // 角色网格
            SliverPadding(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              sliver: SliverLayoutBuilder(
                builder: (context, constraints) {
                  // 计算最佳列数
                  final crossAxisCount = _calculateOptimalColumnCount(constraints.crossAxisExtent);
                  
                  return SliverGrid(
                    gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: crossAxisCount,
                      childAspectRatio: StringsConsts.recommendCardAspectRatio,
                      crossAxisSpacing: StringsConsts.recommendGridSpacing,
                      mainAxisSpacing: StringsConsts.recommendGridSpacing,
                    ),
                    delegate: SliverChildBuilderDelegate(
                      (context, index) {
                        // 性能优化：使用RepaintBoundary减少重绘
                        return RepaintBoundary(
                          child: AiRoleCard(
                            role: roles[index],
                            onTap: () => controller.startChatWithRole(roles[index]),
                            key: ValueKey('role_card_${roles[index].id}'),
                          ),
                        );
                      },
                      childCount: roles.length,
                      // 添加缓存构建项，提高滚动性能
                      addAutomaticKeepAlives: true,
                      addRepaintBoundaries: true,
                    ),
                  );
                },
              ),
            ),
            
            // 加载更多指示器
            SliverToBoxAdapter(
              child: Obx(() {
                if (controller.isLoadingMore.value) {
                  return _buildLoadingMoreIndicator();
                } else if (!controller.hasMoreData.value && roles.isNotEmpty) {
                  // 使用AnimatedOpacity平滑显示"没有更多数据"信息
                  return AnimatedOpacity(
                    opacity: 1.0,
                    duration: const Duration(milliseconds: 300),
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      alignment: Alignment.center,
                      child: Text(
                        MessageConstants.noMoreDataMessage,
                        style: TextStyle(
                          color: Colors.white.withOpacity(0.6),
                          fontSize: 14,
                        ),
                      ),
                    ),
                  );
                } else {
                  return const SizedBox(height: 16);
                }
              }),
            ),
          ],
        ),
      ),
    );
  }
  
  /// 构建加载更多指示器，使用骨架屏
  Widget _buildLoadingMoreIndicator() {
    // 使用新的LoadingMoreSkeleton组件
    return const LoadingMoreSkeleton(
      type: RoleCardSkeletonType.grid,
      childAspectRatio: StringsConsts.recommendCardAspectRatio,
      crossAxisSpacing: StringsConsts.recommendGridSpacing,
      mainAxisSpacing: StringsConsts.recommendGridSpacing,
      padding: EdgeInsets.symmetric(vertical: 12),
    );
  }
  
  /// 根据屏幕宽度计算最佳列数
  int _calculateOptimalColumnCount(double width) {
    if (width < 600) {
      return 2; // 手机屏幕
    } else if (width < 900) {
      return 3; // 小平板
    } else if (width < 1200) {
      return 4; // 大平板
    } else {
      return 5; // 桌面
    }
  }
}

/// 页面生命周期观察者
/// 用于监听页面可见性变化，在页面重新显示时恢复状态
class _PageLifecycleObserver extends WidgetsBindingObserver {
  final RecommendController controller;

  _PageLifecycleObserver(this.controller);

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      // 应用从后台恢复时，检查是否需要刷新数据
      controller.checkAndRestoreState();
    }
  }

  /// 清理Observer - 修复内存泄漏
  static void cleanup() {
    if (RecommendPage._observer != null) {
      WidgetsBinding.instance.removeObserver(RecommendPage._observer!);
      RecommendPage._observer = null;
    }
  }
}