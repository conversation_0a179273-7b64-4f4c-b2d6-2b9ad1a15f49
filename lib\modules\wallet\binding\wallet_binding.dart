import 'package:get/get.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/modules/wallet/repository/wallet_repository.dart';
import 'package:rolio/modules/wallet/service/wallet_service.dart';
import 'package:rolio/modules/wallet/controller/wallet_controller.dart';

/// 钱包模块绑定
class WalletBinding extends Bindings {
  @override
  void dependencies() {
    LogUtil.debug('WalletBinding: 开始注册依赖');
    
    // 注册Repository
    _registerRepository();
    
    // 注册Service
    _registerService();
    
    // 注册Controller
    _registerController();
    
    LogUtil.debug('WalletBinding: 依赖注册完成');
  }

  /// 注册Repository
  void _registerRepository() {
    // 注册本地钱包Repository
    if (!Get.isRegistered<IWalletRepository>()) {
      Get.lazyPut<IWalletRepository>(
        () => LocalWalletRepository(),
        fenix: true,
      );
      LogUtil.debug('WalletBinding: 注册Repository - LocalWalletRepository');
    }
  }

  /// 注册Service
  void _registerService() {
    // 注册钱包服务
    if (!Get.isRegistered<WalletService>()) {
      Get.lazyPut<WalletService>(
        () => WalletService(Get.find<IWalletRepository>()),
        fenix: true,
      );
      LogUtil.debug('WalletBinding: 注册Service - WalletService');
    }
  }

  /// 注册Controller
  void _registerController() {
    // 如果已经存在WalletController，先移除它
    if (Get.isRegistered<WalletController>()) {
      LogUtil.debug('WalletBinding: 移除已存在的WalletController');
      Get.delete<WalletController>(force: true);
    }

    // 注册钱包控制器
    Get.lazyPut<WalletController>(
      () => WalletController(walletService: Get.find<WalletService>()),
      fenix: true,
    );
    LogUtil.debug('WalletBinding: 注册Controller - WalletController');
  }
}
