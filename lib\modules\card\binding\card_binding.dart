import 'package:get/get.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/modules/wallet/binding/wallet_binding.dart';
import 'package:rolio/modules/wallet/service/wallet_service.dart';
import '../repository/card_repository.dart';
import '../service/card_service.dart';
import '../controller/card_controller.dart';

/// Card module binding
class CardBinding extends Bindings {
  @override
  void dependencies() {
    LogUtil.debug('CardBinding: 开始注册依赖');

    // Ensure wallet dependencies are registered first
    _ensureWalletDependencies();

    // Register repository
    Get.lazyPut<ICardRepository>(
      () {
        LogUtil.debug('CardBinding: 注册Repository - LocalCardRepository');
        return LocalCardRepository();
      },
      fenix: true,
    );

    // Register service
    Get.lazyPut<CardService>(
      () {
        LogUtil.debug('CardBinding: 注册Service - CardService');
        return CardService(Get.find<ICardRepository>());
      },
      fenix: true,
    );

    // Register controller
    Get.lazyPut<CardController>(
      () {
        LogUtil.debug('CardBinding: 注册Controller - CardController');
        return CardController();
      },
    );

    LogUtil.debug('CardBinding: 依赖注册完成');
  }

  /// Ensure wallet dependencies are registered
  void _ensureWalletDependencies() {
    try {
      // Check if wallet service is already registered
      Get.find<WalletService>();
      LogUtil.debug('CardBinding: WalletService已存在');
    } catch (e) {
      // If not registered, register wallet dependencies
      LogUtil.debug('CardBinding: WalletService不存在，注册钱包依赖');
      final walletBinding = WalletBinding();
      walletBinding.dependencies();
    }
  }
}
