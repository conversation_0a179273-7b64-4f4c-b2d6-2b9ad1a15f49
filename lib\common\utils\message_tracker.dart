import 'dart:async';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/common/constants/string_constants.dart';

/// 消息跟踪器
/// 
/// 用于跟踪已发送消息及其对应的角色ID，以便处理跨频道消息
class MessageTracker {
  // 单例实例
  static final MessageTracker _instance = MessageTracker._internal();
  
  // 工厂构造函数
  factory MessageTracker() {
    return _instance;
  }
  
  // 内部构造函数
  MessageTracker._internal() {
    _setupCleanupTimer();
  }
  
  // 消息跟踪映射表
  // key: 消息ID, value: {roleId: 角色ID, timestamp: 发送时间戳, conversationId: 会话ID}
  final Map<String, Map<String, dynamic>> _pendingMessages = <String, Map<String, dynamic>>{};
  
  // 角色订阅映射表 - 记录每个角色ID订阅的最大数量
  // key: 角色ID, value: 已订阅数量
  final Map<int, int> _roleSubscriptions = <int, int>{};
  
  // 最大角色订阅数量
  static const int maxRoleSubscriptions = 5;

  // 清理状态 - 使用简单的标记
  bool _isCleanupActive = false;

  // 设置定期清理过期的待处理消息 - 使用Future.delayed循环
  void _setupCleanupTimer() {
    _isCleanupActive = true;
    _scheduleNextCleanup();
  }

  // 调度下一次清理
  void _scheduleNextCleanup() {
    if (!_isCleanupActive) return;

    Future.delayed(Duration(milliseconds: StringsConsts.pendingMessageCleanupInterval), () {
      if (_isCleanupActive) {
        _cleanupPendingMessages();
        _scheduleNextCleanup(); // 递归调度下一次清理
      }
    });
  }
  
  // 清理过期的待处理消息
  void _cleanupPendingMessages() {
    final now = DateTime.now().millisecondsSinceEpoch;
    final expiredMessageIds = <String>[];
    
    // 查找超过配置时间的待处理消息
    _pendingMessages.forEach((messageId, data) {
      final timestamp = data['timestamp'] as int;
      if (now - timestamp > StringsConsts.pendingMessageExpiry) {
        expiredMessageIds.add(messageId);
      }
    });
    
    // 移除过期消息
    for (final messageId in expiredMessageIds) {
      _pendingMessages.remove(messageId);
    }
    
    if (expiredMessageIds.isNotEmpty) {
      LogUtil.debug('已清理${expiredMessageIds.length}条过期待处理消息');
    }
  }
  
  /// 添加待处理消息
  /// 
  /// [messageId] 消息ID
  /// [roleId] 角色ID
  /// [conversationId] 会话ID
  void addPendingMessage(String messageId, int roleId, {int? conversationId}) {
    _pendingMessages[messageId] = {
      'roleId': roleId,
      'conversationId': conversationId,
      'timestamp': DateTime.now().millisecondsSinceEpoch
    };
    LogUtil.debug('添加待处理消息: ID=$messageId, 角色ID=$roleId, 会话ID=$conversationId');
  }
  
  /// 移除待处理消息
  /// 
  /// [messageId] 消息ID
  void removePendingMessage(String messageId) {
    _pendingMessages.remove(messageId);
    LogUtil.debug('移除待处理消息: ID=$messageId');
  }
  
  /// 获取待处理消息的角色ID
  /// 
  /// [messageId] 消息ID
  /// 返回角色ID，如果不存在则返回null
  int? getPendingMessageRoleId(String messageId) {
    final data = _pendingMessages[messageId];
    return data != null ? data['roleId'] as int : null;
  }
  
  /// 获取待处理消息的会话ID
  /// 
  /// [messageId] 消息ID
  /// 返回会话ID，如果不存在则返回null
  int? getPendingMessageConversationId(String messageId) {
    final data = _pendingMessages[messageId];
    return data != null ? data['conversationId'] as int? : null;
  }
  
  /// 检查消息是否为待处理消息
  /// 
  /// [messageId] 消息ID
  /// 返回是否为待处理消息
  bool isPendingMessage(String messageId) {
    return _pendingMessages.containsKey(messageId);
  }
  
  /// 记录角色订阅
  /// 
  /// [roleId] 角色ID
  /// 返回是否允许订阅（未超过最大订阅数）
  bool recordRoleSubscription(int roleId) {
    if (!_roleSubscriptions.containsKey(roleId)) {
      _roleSubscriptions[roleId] = 1;
      return true;
    }
    
    // 检查是否超过最大订阅数
    if (_roleSubscriptions[roleId]! >= maxRoleSubscriptions) {
      LogUtil.warn('角色ID=$roleId 的订阅数已达到最大值 $maxRoleSubscriptions，无法继续订阅');
      return false;
    }
    
    // 增加订阅计数
    _roleSubscriptions[roleId] = _roleSubscriptions[roleId]! + 1;
    LogUtil.debug('角色ID=$roleId 的订阅数增加到 ${_roleSubscriptions[roleId]}');
    return true;
  }
  
  /// 强制移除角色的一个最早的订阅，以便释放空间
  /// 
  /// [roleId] 角色ID
  /// 返回是否成功移除
  bool forceRemoveOldestSubscription(int roleId) {
    if (!_roleSubscriptions.containsKey(roleId)) {
      return false;
    }
    
    // 减少订阅计数
    if (_roleSubscriptions[roleId]! > 0) {
      _roleSubscriptions[roleId] = _roleSubscriptions[roleId]! - 1;
      LogUtil.debug('强制减少角色ID=$roleId 的订阅数到 ${_roleSubscriptions[roleId]}');
      return true;
    }
    
    return false;
  }
  
  /// 移除角色订阅
  /// 
  /// [roleId] 角色ID
  void removeRoleSubscription(int roleId) {
    if (_roleSubscriptions.containsKey(roleId)) {
      _roleSubscriptions[roleId] = _roleSubscriptions[roleId]! - 1;
      
      // 如果订阅数为0，移除记录
      if (_roleSubscriptions[roleId]! <= 0) {
        _roleSubscriptions.remove(roleId);
      }
      
      LogUtil.debug('角色ID=$roleId 的订阅数减少到 ${_roleSubscriptions[roleId] ?? 0}');
    }
  }
  
  /// 获取角色订阅数量
  /// 
  /// [roleId] 角色ID
  /// 返回订阅数量
  int getRoleSubscriptionCount(int roleId) {
    return _roleSubscriptions[roleId] ?? 0;
  }
  
  /// 获取所有已订阅的角色ID
  /// 
  /// 返回角色ID列表
  List<int> getAllSubscribedRoleIds() {
    return _roleSubscriptions.keys.toList();
  }
  
  /// 清空所有待处理消息和角色订阅信息
  /// 
  /// 用于用户登录登出时调用，防止消息残留
  void clearAll() {
    try {
      // 记录清理前的数量，便于调试
      final pendingCount = _pendingMessages.length;
      final roleSubCount = _roleSubscriptions.length;
      
      // 记录所有订阅的角色ID，帮助调试
      final subscribedRoleIds = _roleSubscriptions.keys.toList();
      
      if (pendingCount > 0) {
        LogUtil.debug('准备清理 $pendingCount 条待处理消息:');
        
        // 记录被清理的消息详情
        _pendingMessages.forEach((messageId, data) {
          final roleId = data['roleId'] as int?;
          final conversationId = data['conversationId'];
          LogUtil.debug(' - 清理消息: ID=$messageId, 角色ID=$roleId, 会话ID=$conversationId');
        });
      }
      
      // 记录被清理的角色订阅详情
      if (roleSubCount > 0) {
        LogUtil.debug('准备清理 $roleSubCount 个角色订阅:');
        _roleSubscriptions.forEach((roleId, count) {
          LogUtil.debug(' - 清理角色订阅: 角色ID=$roleId, 订阅数=$count');
        });
      }
      
      // 执行清理
      _pendingMessages.clear();
      _roleSubscriptions.clear();
      
      LogUtil.debug('已清空所有待处理消息(${pendingCount}条)和角色订阅信息(${roleSubCount}个), 清理前的订阅角色: $subscribedRoleIds');
    } catch (e) {
      LogUtil.error('清空待处理消息和角色订阅信息时发生错误: $e');
    }
  }
  
  /// 获取当前待处理消息数量
  /// 
  /// 用于调试和监控
  int getPendingMessageCount() {
    try {
      final count = _pendingMessages.length;
      if (count > 0) {
        // 添加详细日志，帮助调试
        LogUtil.debug('待处理消息计数: $count 条，详情如下:');
        _pendingMessages.forEach((messageId, data) {
          final roleId = data['roleId'] as int?;
          final conversationId = data['conversationId'];
          final timestamp = data['timestamp'] as int?;
          final timeAgo = timestamp != null 
              ? '${(DateTime.now().millisecondsSinceEpoch - timestamp) ~/ 1000}秒前'
              : '未知时间';
          
          LogUtil.debug(' - 待处理消息ID: $messageId, 角色ID: $roleId, 会话ID: $conversationId, 添加于: $timeAgo');
        });
      }
      return count;
    } catch (e) {
      // 如果计数过程中出错，记录错误并返回0
      LogUtil.error('获取待处理消息数量时发生错误: $e');
      return 0;
    }
  }
  
  /// 打印所有待处理消息（用于调试）
  void logAllPendingMessages() {
    if (_pendingMessages.isEmpty) {
      LogUtil.debug('当前没有待处理消息');
      return;
    }
    
    LogUtil.debug('===== 当前待处理消息列表(${_pendingMessages.length}条) =====');
    _pendingMessages.forEach((messageId, data) {
      final roleId = data['roleId'];
      final conversationId = data['conversationId'];
      final timestamp = data['timestamp'] as int;
      final timeAgo = DateTime.now().millisecondsSinceEpoch - timestamp;
      LogUtil.debug('消息ID: $messageId, 角色ID: $roleId, 会话ID: $conversationId, 已等待: ${timeAgo}ms');
    });
    LogUtil.debug('=====================================');
  }
  
  /// 获取特定角色ID的所有待处理消息ID
  /// 
  /// [roleId] 角色ID
  /// 返回该角色的所有待处理消息ID列表
  List<String> getPendingMessageIdsByRoleId(int roleId) {
    final List<String> result = [];
    
    _pendingMessages.forEach((messageId, data) {
      final dataRoleId = data['roleId'];
      if (dataRoleId is int && dataRoleId == roleId) {
        result.add(messageId);
      }
    });
    
    return result;
  }
  
  /// 从待处理列表中移除指定的消息
  /// 
  /// [messageId] 要移除的消息ID
  /// 返回是否成功移除
  bool removeMessageFromPending(String messageId) {
    if (_pendingMessages.containsKey(messageId)) {
      _pendingMessages.remove(messageId);
      LogUtil.debug('已从待处理列表中移除消息: $messageId');
      return true;
    }
    return false;
  }
  
  /// 清除指定角色ID的所有待处理消息
  /// 
  /// [roleId] 角色ID
  /// 返回清除的消息数量
  int clearPendingMessagesByRoleId(int roleId) {
    final messageIds = getPendingMessageIdsByRoleId(roleId);
    int removedCount = 0;
    
    for (final messageId in messageIds) {
      if (removeMessageFromPending(messageId)) {
        removedCount++;
      }
    }
    
    if (removedCount > 0) {
      LogUtil.debug('已清除角色ID=$roleId 的$removedCount条待处理消息');
    }
    
    return removedCount;
  }

  /// 获取所有待处理消息ID列表
  ///
  /// 返回所有待处理消息ID
  List<String> getAllPendingMessageIds() {
    return _pendingMessages.keys.toList();
  }

  /// 释放资源
  void dispose() {
    // 停止清理循环
    _isCleanupActive = false;

    // 清空所有数据
    _pendingMessages.clear();
    _roleSubscriptions.clear();

    LogUtil.debug('MessageTracker 资源已释放');
  }
}