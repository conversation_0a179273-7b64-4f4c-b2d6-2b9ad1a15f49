/// Card rarity enumeration
enum CardRarity {
  common,
  rare,
  epic,
  legendary,
  mythic;

  /// Get rarity display name
  String get displayName {
    switch (this) {
      case CardRarity.common:
        return 'Common';
      case CardRarity.rare:
        return 'Rare';
      case CardRarity.epic:
        return 'Epic';
      case CardRarity.legendary:
        return 'Legendary';
      case CardRarity.mythic:
        return 'Mythic';
    }
  }

  /// Get rarity color
  int get colorValue {
    switch (this) {
      case CardRarity.common:
        return 0xFF9E9E9E; // Grey
      case CardRarity.rare:
        return 0xFF2196F3; // Blue
      case CardRarity.epic:
        return 0xFF9C27B0; // Purple
      case CardRarity.legendary:
        return 0xFFFF9800; // Orange
      case CardRarity.mythic:
        return 0xFFF44336; // Red
    }
  }

  /// Get rarity probability (out of 10000)
  int get probability {
    switch (this) {
      case CardRarity.common:
        return 6000; // 60%
      case CardRarity.rare:
        return 2500; // 25%
      case CardRarity.epic:
        return 1200; // 12%
      case CardRarity.legendary:
        return 280; // 2.8%
      case CardRarity.mythic:
        return 20; // 0.2%
    }
  }

  /// Get rarity stars count
  int get stars {
    switch (this) {
      case CardRarity.common:
        return 1;
      case CardRarity.rare:
        return 2;
      case CardRarity.epic:
        return 3;
      case CardRarity.legendary:
        return 4;
      case CardRarity.mythic:
        return 5;
    }
  }

  /// Get rarity from string
  static CardRarity fromString(String value) {
    return CardRarity.values.firstWhere(
      (rarity) => rarity.name.toLowerCase() == value.toLowerCase(),
      orElse: () => CardRarity.common,
    );
  }
}
