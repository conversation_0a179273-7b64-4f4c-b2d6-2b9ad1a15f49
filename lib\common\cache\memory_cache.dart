import 'dart:async';
import 'dart:convert';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/common/constants/cache_constants.dart';
import 'package:rolio/common/utils/lru_cache.dart';

/// 内存缓存项
class MemoryCacheItem<T> {
  /// 缓存数据
  final T data;
  
  /// 过期时间（毫秒时间戳）
  final int expiryTime;
  
  /// 创建时间（毫秒时间戳）
  final int createTime;
  
  /// 构造函数
  MemoryCacheItem({
    required this.data,
    required this.expiryTime,
  }) : createTime = DateTime.now().millisecondsSinceEpoch;
  
  /// 判断缓存是否过期
  bool isExpired() {
    final now = DateTime.now().millisecondsSinceEpoch;
    return now >= expiryTime;
  }
  
  /// 获取剩余有效时间（毫秒）
  int getRemainingTime() {
    final now = DateTime.now().millisecondsSinceEpoch;
    final remaining = expiryTime - now;
    return remaining > 0 ? remaining : 0;
  }
  
  /// 获取已存在时间（毫秒）
  int getAge() {
    final now = DateTime.now().millisecondsSinceEpoch;
    return now - createTime;
  }
}

/// 内存缓存管理类
///
/// 提供高效的内存缓存实现，支持过期时间和自动清理，使用LRU策略
class MemoryCache {
  /// LRU缓存实例
  late final LRUCache<String, MemoryCacheItem> _lruCache;

  /// 默认过期时间（毫秒）
  int _defaultExpiry = CacheConstants.getMemoryExpiryMs(); // 使用常量定义默认过期时间

  /// 定期清理任务的Timer
  Timer? _cleanupTimer;

  /// 构造函数
  MemoryCache({int? defaultExpiry, int? maxCapacity}) {
    if (defaultExpiry != null && defaultExpiry > 0) {
      _defaultExpiry = defaultExpiry;
    }

    // 初始化LRU缓存
    _lruCache = LRUCache<String, MemoryCacheItem>(
      maxCapacity: maxCapacity ?? 1000, // 默认最大1000个缓存项
      defaultTtl: _defaultExpiry,
      enableStats: true,
    );

    // 启动定期清理任务
    _startCleanupTask();
  }
  
  /// 设置默认过期时间
  void setDefaultExpiry(int milliseconds) {
    if (milliseconds > 0) {
      _defaultExpiry = milliseconds;
    }
  }

  /// 获取LRU缓存统计信息
  LRUCacheStats get stats => _lruCache.stats;

  /// 获取缓存详细信息
  String getCacheInfo() {
    return _lruCache.toString();
  }
  
  /// 获取缓存
  T? get<T>(
    String key, {
    int? maxAge,
    T? Function(Map<String, dynamic>)? fromJson,
  }) {
    final item = _lruCache.get(key);
    if (item == null) {
      return null;
    }

    // 检查是否过期（LRU缓存已经处理了过期检查）
    if (item.isExpired()) {
      return null;
    }

    // 检查最大有效期
    if (maxAge != null) {
      final age = item.getAge();
      if (age > maxAge) {
        _lruCache.remove(key);
        return null;
      }
    }
    
    try {
      // 根据类型处理数据
      if (item.data is T) {
        return item.data as T;
      } 
      
      // 如果需要从JSON转换
      if (fromJson != null && item.data is Map<String, dynamic>) {
        return fromJson(item.data as Map<String, dynamic>);
      } else if (fromJson != null && item.data is String) {
        try {
          final map = jsonDecode(item.data as String) as Map<String, dynamic>;
          return fromJson(map);
        } catch (e) {
          LogUtil.error('内存缓存JSON解析失败: $key, 错误: $e');
          return null;
        }
      }
      
      // 尝试类型转换
      return item.data as T;
    } catch (e) {
      LogUtil.error('内存缓存类型转换失败: $key, 错误: $e');
      return null;
    }
  }
  
  /// 设置缓存
  Future<bool> set<T>(
    String key,
    T data, {
    int? expiry,
    Map<String, dynamic> Function(T)? toJson,
  }) async {
    try {
      final expiryTime = DateTime.now().millisecondsSinceEpoch + (expiry ?? _defaultExpiry);

      final item = MemoryCacheItem(
        data: data,
        expiryTime: expiryTime,
      );

      _lruCache.put(key, item, ttl: expiry);

      return true;
    } catch (e) {
      LogUtil.error('内存缓存设置失败: $key, 错误: $e');
      return false;
    }
  }
  
  /// 删除缓存
  bool remove(String key) {
    return _lruCache.remove(key);
  }

  /// 清空所有缓存
  bool clear() {
    _lruCache.clear();
    return true;
  }

  /// 检查缓存是否存在且未过期
  bool exists(String key) {
    return _lruCache.containsKey(key);
  }
  
  /// 获取缓存条目数量
  int size() {
    return _lruCache.length;
  }

  /// 获取所有缓存键
  List<String> getKeys() {
    return _lruCache.keys.toList();
  }

  /// 获取过期时间
  int? getExpiryTime(String key) {
    final item = _lruCache.get(key);
    if (item == null || item.isExpired()) {
      return null;
    }
    return item.expiryTime;
  }
  
  /// 清理过期缓存
  void _cleanupExpiredItems() {
    try {
      final expiredCount = _lruCache.cleanupExpired();

      if (expiredCount > 0) {
        LogUtil.debug('内存缓存清理完成，移除了 $expiredCount 个过期项');
      }
    } catch (e) {
      LogUtil.error('内存缓存清理失败: $e');
    }
  }
  
  /// 启动定期清理任务
  void _startCleanupTask() {
    // 取消之前的定时器（如果存在）
    _cleanupTimer?.cancel();

    // 每分钟清理一次过期缓存，使用Timer.periodic避免递归调用
    _cleanupTimer = Timer.periodic(const Duration(minutes: 1), (timer) {
      _cleanupExpiredItems();
    });

    LogUtil.debug('内存缓存定期清理任务已启动，间隔: 1分钟');
  }

  /// 停止定期清理任务
  void stopCleanupTask() {
    _cleanupTimer?.cancel();
    _cleanupTimer = null;
    LogUtil.debug('内存缓存定期清理任务已停止');
  }

  /// 销毁缓存，释放资源
  void dispose() {
    stopCleanupTask();
    _lruCache.clear();
    LogUtil.debug('内存缓存已销毁');
  }
}