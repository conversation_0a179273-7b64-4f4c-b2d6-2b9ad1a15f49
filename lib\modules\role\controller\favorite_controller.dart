import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:rolio/common/interfaces/chat_service_interface.dart';
import 'package:rolio/common/models/ai_role.dart';
import 'package:rolio/common/utils/error_handler.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/common/utils/image_preloader.dart';
import 'package:rolio/common/utils/toast_util.dart'; // 添加ToastUtil导入
import 'package:rolio/common/utils/pagination_mixin.dart';
import 'package:rolio/common/models/page_request.dart';
import 'package:rolio/manager/global_state.dart';
import 'package:rolio/modules/role/service/role_service.dart';
import 'package:rolio/common/state/global_event_state.dart';

/// 收藏角色控制器
/// 负责管理收藏角色页面的UI状态和业务逻辑
class FavoriteController extends GetxController with PaginationMixin {
  // 服务和管理器 - 通过GetX获取
  late final RoleService roleService;
  IChatService? chatService;
  late final GlobalState globalState;
  late final GlobalEventState globalEventState;
  
  // Workers列表 - 用于监听事件
  final List<Worker> _workers = [];

  // 资源清理状态标记
  bool _disposed = false;
  
  // 收藏角色列表
  final RxList<AiRole> favoriteRoles = <AiRole>[].obs;
  
  // 加载状态
  final RxBool isLoading = false.obs;
  
  // 是否显示骨架屏 - 控制UI显示
  final RxBool showSkeleton = true.obs;
  
  // 图片预加载器
  ImagePreloader get _imagePreloader => Get.find<ImagePreloader>();
  
  // 分页相关
  final int pageSize = 10; // 每页加载数量
  int _currentPage = 1;
  int _totalItems = 0;
  int _totalPages = 0;
  
  // 分页状态由PaginationMixin提供
  
  // 最后一次刷新时间戳，防止频繁刷新
  int _lastRefreshTime = 0;
  static const int minRefreshInterval = 5000; // 5秒
  

  
  // 图片预加载器待清理URL列表 - 优化版本
  final Set<String> _preloadedImageUrls = <String>{};

  // 预加载URL的最大数量限制
  static const int _maxPreloadedUrls = 50;

  // 上次清理预加载URL的时间
  DateTime? _lastPreloadCleanupTime;
  
  /// 初始化状态
  void _initState() {
    // 初始化全局状态
    globalState = Get.find<GlobalState>();
    globalEventState = Get.find<GlobalEventState>();

    // 初始化分页状态
    initializePagination(
      initialRequest: const PageRequest(page: 1, size: 20),
      hasMore: true,
    );

    // 设置收藏状态变更监听器
    _setupFavoriteStateListener();
    
    // 尝试获取ChatService，如果不可用不阻止初始化
    try {
      if (Get.isRegistered<IChatService>()) {
        chatService = Get.find<IChatService>();
        LogUtil.debug('FavoriteController: 已获取IChatService');
      } else {
        LogUtil.warn('FavoriteController: IChatService未注册，聊天功能可能不可用');
      }
    } catch (e) {
      LogUtil.warn('FavoriteController: 获取IChatService失败: $e');
    }
  }
  
  /// 设置收藏状态变更监听器
  /// 统一监听GlobalFavoriteStateManager的状态变更通知
  void _setupFavoriteStateListener() {
    // 监听全局状态管理器的状态变更通知
    final stateChangeWorker = ever(roleService.globalFavoriteManager.stateChangeNotifier, (_) {
      _syncWithGlobalState();
    });
    _workers.add(stateChangeWorker);

    // 监听收藏状态缓存的直接变化（兼容性保留）
    final favoriteWorker = ever(roleService.globalFavoriteManager.favoriteStatusCache, (Map<int, bool> cache) {
      _syncWithGlobalState();
    });
    _workers.add(favoriteWorker);
  }

  /// 与全局状态同步（统一的同步逻辑）
  void _syncWithGlobalState() {
    bool hasChanges = false;
    final globalManager = roleService.globalFavoriteManager;

    for (int i = 0; i < favoriteRoles.length; i++) {
      final role = favoriteRoles[i];
      final cachedStatus = globalManager.getFavoriteStatus(role.id);

      if (cachedStatus != null && cachedStatus != role.isFavorited) {
        LogUtil.debug('FavoriteController: 同步收藏状态变更 roleId=${role.id}, newStatus=$cachedStatus');

        if (cachedStatus) {
          // 角色被收藏，更新状态
          favoriteRoles[i] = role.copyWith(isFavorited: true);
          hasChanges = true;
        } else {
          // 角色被取消收藏，从列表中移除
          favoriteRoles.removeAt(i);
          _totalItems -= 1;
          hasChanges = true;
          i--; // 调整索引，因为移除了一个元素
        }
      }
    }

    if (hasChanges) {
      update(); // 通知UI更新
    }
  }
  
  /// 更新角色收藏状态（简化版本，统一使用全局状态）
  void _updateRoleFavoriteStatus(int roleId, bool isFavorited) {
    // 直接更新全局状态管理器，让监听器自动同步UI
    roleService.globalFavoriteManager.setFavoriteStatus(roleId, isFavorited);
    LogUtil.debug('FavoriteController: 已更新全局收藏状态 roleId=$roleId, status=$isFavorited');
  }
  
  /// 从列表中移除角色
  void _removeRoleFromList(int roleId) {
    final index = favoriteRoles.indexWhere((role) => role.id == roleId);
    if (index != -1) {
      favoriteRoles.removeAt(index);
      _totalItems -= 1;
      update();
    }
  }
  
  /// 加载收藏角色列表
  /// 
  /// [forceRefresh] 是否强制刷新，忽略缓存
  Future<void> loadFavoriteRoles({bool forceRefresh = false}) async {
    try {
      // 如果正在加载，不重复请求
      if (isLoading.value && !forceRefresh) return;
      
      // 重置分页
      _currentPage = 1;
      hasMoreData.value = true;
      
      // 设置加载状态
      isLoading.value = true;
      
      LogUtil.debug('加载收藏角色列表，页码: $_currentPage，每页数量: $pageSize');
      
      // 调用服务获取收藏角色
      final result = await roleService.getFavoritedRoles(page: _currentPage, size: pageSize);
      
      final roles = result['items'] as List<AiRole>;
      _totalItems = result['total'] as int;
      _totalPages = result['pages'] as int;
      
      // 更新列表
      favoriteRoles.clear();
      favoriteRoles.addAll(roles);
      
      // 判断是否有更多数据
      hasMoreData.value = _currentPage < _totalPages;
      
      // 数据加载完成后，预加载图片
      if (roles.isNotEmpty) {
        _preloadRoleImages(roles);
      }
      
      // 更新刷新时间
      _lastRefreshTime = DateTime.now().millisecondsSinceEpoch;
      
      // 数据加载完成后，无论列表是否为空都隐藏骨架屏
      if (showSkeleton.value) {
        Future.delayed(const Duration(milliseconds: 300), () {
          showSkeleton.value = false;
        });
      }
      

      
      LogUtil.debug('收藏角色列表加载完成，共${roles.length}个角色，总共$_totalItems个');
      
    } catch (e) {
      LogUtil.error('加载收藏角色列表失败: $e');
      ErrorHandler.handleException(e);
    } finally {
      isLoading.value = false;
    }
  }
  
  /// 刷新收藏角色列表
  /// 
  /// 用于下拉刷新
  Future<void> refreshFavoriteRoles() async {
    // 检查是否达到刷新间隔时间
    final now = DateTime.now().millisecondsSinceEpoch;
    if (now - _lastRefreshTime < minRefreshInterval) {
      LogUtil.debug('刷新间隔太短，跳过刷新');
      return;
    }
    
    await loadFavoriteRoles(forceRefresh: true);
  }
  
  /// 加载更多收藏角色 - 使用PaginationMixin
  Future<void> loadMoreFavoriteRoles() async {
    await handleLoadMore(_loadMoreFavoriteRolesImpl);
  }

  /// 实际的加载更多逻辑实现
  Future<bool> _loadMoreFavoriteRolesImpl(PageRequest request) async {
    try {
      LogUtil.debug('加载更多收藏角色，页码: ${request.page}，每页数量: ${request.size}');

      // 获取下一页数据
      final result = await roleService.getFavoritedRoles(page: request.page, size: request.size);

      final roles = result['items'] as List<AiRole>;
      _totalItems = result['total'] as int;
      _totalPages = result['pages'] as int;

      // 添加到列表
      favoriteRoles.addAll(roles);

      // 更新分页状态
      updateHasMoreData(request.page < _totalPages);

      // 预加载新加载角色的图片
      if (roles.isNotEmpty) {
        _preloadRoleImages(roles);
      }

      LogUtil.debug('加载更多收藏角色完成，本次加载${roles.length}个，总共${favoriteRoles.length}个');
      return true;

    } catch (e) {
      LogUtil.error('加载更多收藏角色失败: $e');
      ErrorHandler.handleException(e);
      return false;
    }
  }
  
  /// 处理滚动通知，用于滚动到底部时加载更多
  bool handleScrollNotification(ScrollNotification notification) {
    if (notification is ScrollEndNotification) {
      if (notification.metrics.pixels >= notification.metrics.maxScrollExtent - 200) {
        loadMoreFavoriteRoles();
      }
    }
    return false;
  }
  
  /// 获取角色列表
  /// 
  /// 用于UI绑定
  List<AiRole> getRoles() {
    return favoriteRoles.toList();
  }
  
  /// 预加载角色图片 - 优化版本，只预加载可视区域图片
  void _preloadRoleImages(List<AiRole> roles) {
    // 使用智能预加载策略，避免内存压力
    _preloadVisibleImages(roles);
  }

  /// 预加载可视区域的角色图片
  void _preloadVisibleImages(List<AiRole> roles) {
    // 计算可视区域应该预加载的角色数量（通常是一屏+下一屏的部分）
    final visibleCount = _getVisibleRoleCount();
    final preloadCount = (visibleCount * 1.5).ceil(); // 预加载1.5屏的内容

    // 限制预加载数量，避免内存压力
    final actualPreloadCount = preloadCount.clamp(0, roles.length);

    LogUtil.debug('收藏页面预加载策略: 总角色${roles.length}个，预加载前${actualPreloadCount}个');

    for (int i = 0; i < actualPreloadCount; i++) {
      final role = roles[i];

      if (role.avatarUrl.isNotEmpty) {
        _imagePreloader.preloadImage(
          role.avatarUrl,
          priority: i < visibleCount ? ImagePreloadPriority.high : ImagePreloadPriority.medium,
        );
        // 记录预加载的URL，方便后续清理
        _addPreloadedUrl(role.avatarUrl);
      }

      if (role.coverUrl.isNotEmpty) {
        _imagePreloader.preloadImage(
          role.coverUrl,
          priority: i < visibleCount ? ImagePreloadPriority.high : ImagePreloadPriority.medium,
        );
        // 记录预加载的URL，方便后续清理
        _addPreloadedUrl(role.coverUrl);
      }
    }
  }

  /// 计算可视区域的角色数量
  int _getVisibleRoleCount() {
    // 基于常见的移动设备屏幕和角色卡片大小估算
    // 假设每行2个角色，每屏大约显示6-8个角色
    return 8; // 保守估计一屏显示8个角色
  }

  /// 智能添加预加载URL - 带自动清理机制
  void _addPreloadedUrl(String url) {
    if (url.isEmpty) return;

    // 检查是否需要清理
    _checkAndCleanupPreloadedUrls();

    // 添加URL
    _preloadedImageUrls.add(url);
  }

  /// 检查并清理预加载URL集合
  void _checkAndCleanupPreloadedUrls() {
    final now = DateTime.now();

    // 检查数量限制
    if (_preloadedImageUrls.length >= _maxPreloadedUrls) {
      _performPreloadCleanup('数量超限');
      return;
    }

    // 检查时间间隔（每5分钟清理一次）
    if (_lastPreloadCleanupTime != null) {
      final timeSinceLastCleanup = now.difference(_lastPreloadCleanupTime!);
      if (timeSinceLastCleanup.inMinutes >= 5) {
        _performPreloadCleanup('定期清理');
      }
    } else {
      _lastPreloadCleanupTime = now;
    }
  }

  /// 执行预加载URL清理
  void _performPreloadCleanup(String reason) {
    try {
      final beforeCount = _preloadedImageUrls.length;

      // 清理一半的URL（保留最近的）
      if (_preloadedImageUrls.length > 20) {
        final urlsList = _preloadedImageUrls.toList();
        final keepCount = (_preloadedImageUrls.length * 0.5).ceil();
        final urlsToKeep = urlsList.skip(urlsList.length - keepCount).toSet();

        _preloadedImageUrls.clear();
        _preloadedImageUrls.addAll(urlsToKeep);
      }

      _lastPreloadCleanupTime = DateTime.now();

      final afterCount = _preloadedImageUrls.length;
      LogUtil.debug('预加载URL清理完成($reason): ${beforeCount} -> $afterCount');

    } catch (e) {
      LogUtil.error('预加载URL清理失败: $e');
    }
  }

  /// 切换角色收藏状态（简化版本）
  Future<void> toggleFavorite(AiRole role) async {
    try {
      LogUtil.debug('切换角色收藏状态: ${role.name} (ID: ${role.id})');

      // 调用服务层统一处理收藏状态切换
      final newState = await roleService.toggleFavorite(role.id);

      if (newState == null) {
        // 操作失败
        LogUtil.warn('收藏状态切换失败: ${role.id}');
        ToastUtil.error('Failed to change favorite status');
        return;
      }

      // 更新本地UI状态
      _updateRoleFavoriteStatus(role.id, newState);

      // 如果是取消收藏状态，从列表中移除
      if (!newState) {
        _removeRoleFromList(role.id);
        ToastUtil.success('Removed from favorites');
      } else {
        ToastUtil.success('Added to favorites');
      }
    } catch (e) {
      LogUtil.error('切换角色收藏状态失败: $e');
      ErrorHandler.handleException(e);
    }
  }
  
  /// 检查更新
  /// 当应用恢复到前台或页面重新可见时调用
  Future<void> checkForUpdates() async {
    // 检查是否需要刷新
    final now = DateTime.now().millisecondsSinceEpoch;
    if (now - _lastRefreshTime > minRefreshInterval) {
      LogUtil.debug('页面恢复可见，刷新收藏角色列表');
      await refreshFavoriteRoles();
    }
  }
  
  /// 验证并同步全局收藏状态（简化版本）
  /// 直接调用统一的同步逻辑
  void syncWithGlobalFavoriteState() {
    _syncWithGlobalState();
  }
  
  @override
  void onInit() {
    super.onInit();
    
    // 获取服务实例
    roleService = Get.find<RoleService>();
    
    // 初始化状态
    _initState();
    
    // 加载收藏角色列表
    loadFavoriteRoles();
    
    // 初始化完成后同步全局状态 - 修复内存泄漏风险
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // 检查Controller是否已销毁
      if (!_disposed) {
        syncWithGlobalFavoriteState();
      }
    });
  }
  
  @override
  void onClose() {
    // 防止重复清理
    if (_disposed) {
      LogUtil.debug('FavoriteController: 资源已清理，跳过重复清理');
      return;
    }
    _disposed = true;

    try {
      // 清理所有Workers
      for (final worker in _workers) {
        try {
          worker.dispose();
        } catch (e) {
          LogUtil.error('FavoriteController: Worker清理失败: $e');
        }
      }
      _workers.clear();

      // 清理预加载的图片资源 - 优化版本
      if (_preloadedImageUrls.isNotEmpty) {
        LogUtil.debug('清理预加载的图片资源: ${_preloadedImageUrls.length}张');
        try {
          // 只清理当前Controller预加载的图片，而不是整个内存缓存
          // 注意：ImagePreloader的内部缓存管理比较复杂，直接清理整个内存缓存更安全
          _imagePreloader.clearMemoryCache();
          _preloadedImageUrls.clear();
          LogUtil.debug('FavoriteController: 预加载图片资源清理完成');
        } catch (e) {
          LogUtil.error('FavoriteController: 图片缓存清理失败: $e');
        }
      }

      // 清理分页资源
      disposePagination();

      // 清理页面Observer - 修复内存泄漏
      try {
        // 通过反射或导入清理Observer
        // 注意：这里需要导入view文件，但为了避免循环依赖，我们在Controller中不直接调用
        // 实际的清理应该在页面销毁时进行
        LogUtil.debug('FavoriteController: 页面Observer应在页面销毁时清理');
      } catch (e) {
        LogUtil.error('FavoriteController: 页面Observer清理失败: $e');
      }

      // 记录日志
      LogUtil.debug('FavoriteController: 已取消所有事件订阅并清理资源');
    } catch (e) {
      LogUtil.error('FavoriteController: 资源清理过程中发生错误: $e');
    } finally {
      super.onClose();
    }
  }
} 