import 'card_rarity.dart';

/// Card data model
class Card {
  final String id;
  final String name;
  final String description;
  final String imageUrl;
  final CardRarity rarity;
  final String roleId;
  final String roleName;
  final Map<String, dynamic> attributes;
  final DateTime createdAt;
  final bool isOwned;
  final int ownedCount;

  const Card({
    required this.id,
    required this.name,
    required this.description,
    required this.imageUrl,
    required this.rarity,
    required this.roleId,
    required this.roleName,
    this.attributes = const {},
    required this.createdAt,
    this.isOwned = false,
    this.ownedCount = 0,
  });

  /// Create card from map
  factory Card.fromMap(Map<String, dynamic> map) {
    return Card(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      description: map['description'] ?? '',
      imageUrl: map['image_url'] ?? '',
      rarity: CardRarity.fromString(map['rarity'] ?? 'common'),
      roleId: map['role_id'] ?? '',
      roleName: map['role_name'] ?? '',
      attributes: Map<String, dynamic>.from(map['attributes'] ?? {}),
      createdAt: DateTime.tryParse(map['created_at'] ?? '') ?? DateTime.now(),
      isOwned: map['is_owned'] ?? false,
      ownedCount: map['owned_count'] ?? 0,
    );
  }

  /// Convert card to map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'image_url': imageUrl,
      'rarity': rarity.name,
      'role_id': roleId,
      'role_name': roleName,
      'attributes': attributes,
      'created_at': createdAt.toIso8601String(),
      'is_owned': isOwned,
      'owned_count': ownedCount,
    };
  }

  /// Create copy with updated fields
  Card copyWith({
    String? id,
    String? name,
    String? description,
    String? imageUrl,
    CardRarity? rarity,
    String? roleId,
    String? roleName,
    Map<String, dynamic>? attributes,
    DateTime? createdAt,
    bool? isOwned,
    int? ownedCount,
  }) {
    return Card(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      imageUrl: imageUrl ?? this.imageUrl,
      rarity: rarity ?? this.rarity,
      roleId: roleId ?? this.roleId,
      roleName: roleName ?? this.roleName,
      attributes: attributes ?? this.attributes,
      createdAt: createdAt ?? this.createdAt,
      isOwned: isOwned ?? this.isOwned,
      ownedCount: ownedCount ?? this.ownedCount,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Card && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Card(id: $id, name: $name, rarity: ${rarity.displayName})';
  }
}
