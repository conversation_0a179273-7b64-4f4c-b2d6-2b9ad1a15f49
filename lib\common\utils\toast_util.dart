import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:rolio/common/utils/logger.dart';

/// 轻量级提示工具类
class ToastUtil {
  /// 私有构造函数，防止实例化
  ToastUtil._();

  /// 显示轻量级提示
  static void showToast(
    String message, {
    ToastType type = ToastType.info,
    Duration duration = const Duration(seconds: 1),
    SnackPosition position = SnackPosition.TOP,
    double? width,
  }) {
    try {
      // 根据类型设置颜色和图标
      Color backgroundColor;
      Color textColor = Colors.white;
      IconData? icon;

      switch (type) {
        case ToastType.success:
          backgroundColor = Colors.green.shade600;
          icon = Icons.check_circle_outline;
          break;
        case ToastType.error:
          backgroundColor = Colors.red.shade600;
          icon = Icons.error_outline;
          break;
        case ToastType.warning:
          backgroundColor = Colors.orange.shade600;
          icon = Icons.warning_amber_outlined;
          break;
        case ToastType.info:
        default:
          backgroundColor = Colors.blue.shade600;
          icon = Icons.info_outline;
          break;
      }

      // 使用GetX显示轻量级提示，添加现代淡入淡出效果
      Get.rawSnackbar(
        messageText: Row(
          children: [
            if (icon != null) ...[
              Icon(icon, color: textColor),
              const SizedBox(width: 8),
            ],
            Expanded(
              child: Text(
                message,
                style: TextStyle(color: textColor),
              ),
            ),
          ],
        ),
        duration: duration,
        backgroundColor: backgroundColor,
        margin: const EdgeInsets.all(10),
        borderRadius: 12, // 圆角
        snackPosition: position,
        snackStyle: SnackStyle.FLOATING,
        dismissDirection: DismissDirection.horizontal, // 允许水平方向滑动关闭
        // 淡入淡出效果
        forwardAnimationCurve: Curves.easeOutQuad,
        reverseAnimationCurve: Curves.easeOutQuart, // 使消失动画更丝滑
        animationDuration: const Duration(milliseconds: 300), // 更快的动画时间
        barBlur: 0.0,
        overlayBlur: 0.0,
        overlayColor: Colors.transparent,
        isDismissible: true, // 可以点击关闭
        shouldIconPulse: false, // 防止图标闪烁
        // 点击关闭
        onTap: (_) {
          Get.closeCurrentSnackbar();
        },
        showProgressIndicator: false,
        leftBarIndicatorColor: Colors.transparent,
        maxWidth: width ?? Get.width * 0.85,
      );

      LogUtil.debug('显示提示: $message, 类型: $type');
    } catch (e) {
      LogUtil.error('显示提示失败: $e');
    }
  }

  /// 显示成功提示
  static void success(String message) {
    showToast(message, type: ToastType.success, duration: const Duration(milliseconds: 1500));
  }

  /// 显示错误提示
  static void error(String message) {
    showToast(message, type: ToastType.error, duration: const Duration(seconds: 2));
  }

  /// 显示警告提示
  static void warning(String message) {
    showToast(message, type: ToastType.warning, duration: const Duration(milliseconds: 1500));
  }

  /// 显示信息提示
  static void info(String message) {
    showToast(message, type: ToastType.info, duration: const Duration(milliseconds: 1500));
  }
}

/// 提示类型
enum ToastType {
  /// 成功提示
  success,

  /// 错误提示
  error,

  /// 警告提示
  warning,

  /// 信息提示
  info,
} 