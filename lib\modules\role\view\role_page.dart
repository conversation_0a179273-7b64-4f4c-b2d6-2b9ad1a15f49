import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/modules/role/controller/role_controller.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:rolio/routes/routes.dart';

/// 角色详情页
class RolePage extends GetView<RoleController> {
  const RolePage({Key? key}) : super(key: key);

  /// Navigate to role gacha page
  void _navigateToRoleGacha() {
    final role = controller.currentRole;
    if (role != null) {
      Get.toNamed(Routes.roleGachaScreen, arguments: role);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(
            Icons.arrow_back,
            color: Colors.white,
            shadows: [
              Shadow(
                color: Colors.black,
                offset: Offset(0, 1),
                blurRadius: 3.0,
              ),
            ],
          ),
          onPressed: () => Get.back(),
        ),
        actions: [
          // 抽卡按钮
          IconButton(
            icon: const Icon(
              Icons.casino,
              color: Colors.white,
              shadows: [
                Shadow(
                  color: Colors.black,
                  offset: Offset(0, 1),
                  blurRadius: 3.0,
                ),
              ],
            ),
            onPressed: () => _navigateToRoleGacha(),
          ),
          // 收藏按钮
          Obx(() {
            final role = controller.currentRole;
            final isFavorited = role?.isFavorited ?? false;
            final isLoading = controller.isFavoriteLoading;
            
            return IconButton(
              icon: isLoading 
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      color: Colors.white,
                      strokeWidth: 2,
                    ),
                  )
                : Icon(
                    isFavorited ? Icons.favorite : Icons.favorite_border,
                    color: isFavorited ? Colors.red : Colors.white,
                    shadows: const [
                      Shadow(
                        color: Colors.black,
                        offset: Offset(0, 1),
                        blurRadius: 3.0,
                      ),
                    ],
                  ),
              onPressed: isLoading
                ? null // 加载中禁用按钮
                : () {
                    LogUtil.debug('点击收藏按钮，当前状态: $isFavorited');
                    controller.toggleFavorite();
                  },
            );
          }),
          IconButton(
            icon: const Icon(
              Icons.more_horiz,
              color: Colors.white,
              shadows: [
                Shadow(
                  color: Colors.black,
                  offset: Offset(0, 1),
                  blurRadius: 3.0,
                ),
              ],
            ),
            onPressed: () {},
          ),
        ],
      ),
      body: Obx(() {
        if (controller.isLoading) {
          return const Center(
            child: CircularProgressIndicator(
              color: Colors.white,
            ),
          );
        }

        if (controller.hasError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  controller.errorMessage,
                  style: const TextStyle(
                    fontSize: 16,
                    color: Colors.grey,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () {
                    controller.retryGetRoleDetail();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('Retry'),
                ),
              ],
            ),
          );
        }

        final role = controller.currentRole;
        if (role == null) {
          return const Center(
            child: Text(
              'Role not found',
              style: TextStyle(color: Colors.white),
            ),
          );
        }

        return SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 封面图片
              Stack(
                children: [
                  // 封面图片
                  SizedBox(
                    width: double.infinity,
                    height: 400, // 减小高度，使布局更紧凑
                    child: ClipRect(
                      child: OverflowBox(
                        alignment: Alignment.topCenter, // 顶部对齐
                        maxHeight: 600, // 允许溢出的最大高度
                        child: _buildCoverImage(role.coverUrl),
                      ),
                    ),
                  ),
                  // 渐变遮罩
                  Positioned.fill(
                    child: Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                                                  colors: [
                          Colors.transparent,
                          Colors.black.withAlpha(51),  // 0.2 * 255 = 51
                          Colors.black.withAlpha(128), // 0.5 * 255 = 128
                          Colors.black.withAlpha(204), // 0.8 * 255 = 204
                          Colors.black,
                        ],
                        stops: const [0.2, 0.4, 0.6, 0.8, 1.0],
                        ),
                      ),
                    ),
                  ),
                  // 角色名称
                  Positioned(
                    bottom: 20, // 将名称位置上移，原来是70
                    left: 16,
                    right: 16, // 添加右侧限制
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          width: MediaQuery.of(context).size.width - 32, // 限制宽度为屏幕宽度减去左右边距
                          child: Text(
                            role.name,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 42, // 增大字体
                              fontWeight: FontWeight.bold,
                              shadows: [
                                Shadow(
                                  color: Colors.black,
                                  offset: Offset(0, 1),
                                  blurRadius: 4.0,
                                ),
                              ],
                            ),
                            softWrap: true, // 允许文本换行
                            overflow: TextOverflow.ellipsis, // 超出部分显示省略号
                            maxLines: 2, // 最多显示两行
                            textAlign: TextAlign.start, // 文本左对齐
                          ),
                        ),
                        const SizedBox(height: 8),

                        // 不显示音调标签
                      ],
                    ),
                  ),
                ],
              ),
              // 角色信息
              Padding(
                padding: const EdgeInsets.only(left: 16, right: 16, top: 0, bottom: 16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 对话数量和消息数量
                    Row(
                      children: [
                        Expanded(
                          child: Container(
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            decoration: BoxDecoration(
                              color: Colors.grey[900],
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Column(
                              children: [
                                Text(
                                  role.conversationCount,
                                  style: const TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.white,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                const Text(
                                  'dialogues',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.grey,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Container(
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            decoration: BoxDecoration(
                              color: Colors.grey[900],
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Column(
                              children: [
                                Text(
                                  role.chatCount,
                                  style: const TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.white,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                const Text(
                                  'chatters',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.grey,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 24),
                    // 角色描述标题
                    const Text(
                      'About',
                      style: TextStyle(
                        fontSize: 24, // 增大字体
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 12),
                    // 角色描述
                    Text(
                      role.description,
                      style: const TextStyle(
                        fontSize: 16,
                        color: Colors.white70,
                        height: 1.5,
                      ),
                    ),
                    const SizedBox(height: 32), // 增加间距
                    // 角色问候语标题
                    if (role.greeting != null && role.greeting!.isNotEmpty) ...[
                      const Text(
                        'Greeting',
                        style: TextStyle(
                          fontSize: 24, // 增大字体
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(height: 12),
                      // 角色问候语
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.grey[850], // 稍微调亮一点
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: Colors.grey[800]!,
                            width: 1,
                          ),
                        ),
                        child: Text(
                          role.greeting!,
                          style: const TextStyle(
                            fontSize: 16,
                            color: Colors.white70,
                            height: 1.5,
                          ),
                        ),
                      ),
                      const SizedBox(height: 32), // 增加间距
                    ],
                    // 已删除语气标签部分
                  ],
                ),
              ),
            ],
          ),
        );
      }),
    );
  }
  
  /// 构建封面图片，利用预加载状态优化加载速度
  Widget _buildCoverImage(String imageUrl) {
    // 检查图片是否已预加载
    final bool isPreloaded = controller.isCoverPreloaded;
    
    return CachedNetworkImage(
      imageUrl: imageUrl,
      width: double.infinity,
      fit: BoxFit.cover,
      alignment: Alignment.topCenter, // 顶部对齐，确保显示上半部分
      fadeInDuration: isPreloaded 
          ? Duration.zero // 如果已预加载，立即显示
          : const Duration(milliseconds: 300), // 否则使用淡入效果
      placeholder: (context, url) => Container(
        color: Colors.black,
        width: double.infinity,
        height: 550,
        child: const Center(
          child: CircularProgressIndicator(
            color: Colors.white,
          ),
        ),
      ),
      errorWidget: (context, url, error) {
        LogUtil.error('加载封面图失败: $error');
        return Container(
          color: Colors.black,
          width: double.infinity,
          height: 550,
        );
      },
    );
  }
}
