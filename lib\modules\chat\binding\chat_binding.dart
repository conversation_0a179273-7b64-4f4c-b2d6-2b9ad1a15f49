import 'package:get/get.dart';
import 'package:rolio/common/cache/cache_manager.dart';
import 'package:rolio/common/interfaces/chat_service_interface.dart';
import 'package:rolio/common/interfaces/session_provider.dart';
import 'package:rolio/common/services/role_provider.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/manager/global_state.dart';
import 'package:rolio/modules/chat/controller/chat_controller.dart';
import 'package:rolio/modules/chat/controller/report_controller.dart';
import 'package:rolio/modules/chat/repository/chat_repository.dart';
import 'package:rolio/modules/chat/repository/report_repository.dart';
import 'package:rolio/modules/chat/service/chat_manager.dart';
import 'package:rolio/modules/chat/service/chat_service.dart';
import 'package:rolio/modules/chat/service/message_service.dart';
import 'package:rolio/modules/chat/service/report_service.dart';
import 'package:rolio/modules/sessions/binding/sessions_binding.dart';
import 'package:rolio/modules/role/binding/recommend_binding.dart';
import 'package:rolio/modules/role/service/recommend_service.dart';
import 'package:rolio/modules/chat/service/ai_channel_manager.dart';
import 'package:rolio/common/di/service_bindings.dart';

/// 聊天模块绑定
///
/// 负责注册聊天模块所需的依赖
class ChatBinding extends Bindings {
  @override
  void dependencies() {
    try {
      LogUtil.info('开始注册ChatBinding依赖...');

      // 确保会话服务已注册 - 使用SessionsBinding注册
      _ensureSessionsBindingRegistered();

      // 确保推荐服务已注册 - 使用RecommendBinding注册，这样角色切换功能可以正常工作
      _ensureRecommendBindingRegistered();

      _registerRepositories();
      _registerServices();
      _registerControllers();

      // 清理聊天相关的持久化缓存
      _cleanupChatPersistentCache();


      LogUtil.info('ChatBinding依赖注册完成');
    } catch (e) {
      LogUtil.error('ChatBinding依赖注册失败: $e');
      rethrow;
    }
  }

  /// 确保会话绑定已注册
  void _ensureSessionsBindingRegistered() {
    if (!Get.isRegistered<ISessionProvider>()) {
      LogUtil.info('注册SessionsBinding...');
      final sessionsBinding = SessionsBinding();
      sessionsBinding.dependencies();
      LogUtil.debug('已确保SessionsBinding依赖已注册');
    } else {
      LogUtil.debug('SessionsBinding依赖已存在，跳过注册');
    }
  }

  /// 确保推荐绑定已注册
  void _ensureRecommendBindingRegistered() {
    if (!Get.isRegistered<RecommendService>()) {
      LogUtil.info('注册RecommendBinding...');
      final recommendBinding = RecommendBinding();
      recommendBinding.dependencies();
      LogUtil.debug('已确保RecommendBinding依赖已注册');
    } else {
      LogUtil.debug('RecommendBinding依赖已存在，跳过注册');
    }
  }

  void _registerRepositories() {
    if (!Get.isRegistered<ChatRepository>()) {
      Get.put<ChatRepository>(ChatRepository(), permanent: true);
      LogUtil.debug('注册仓库: ChatRepository');
    }

    // 注册举报仓库
    if (!Get.isRegistered<ReportRepository>()) {
      Get.put<ReportRepository>(ReportRepository(), permanent: true);
      LogUtil.debug('注册仓库: ReportRepository');
    }
  }

  void _registerServices() {
    // 确保RoleProvider已注册 - 在ServiceBindings中已经处理过IRoleProvider依赖
    // 无需额外处理RoleProvider的注册逻辑

    // 注册AiChannelManager - 必须在ChatManager之前注册
    if (!Get.isRegistered<AiChannelManager>()) {
      Get.put<AiChannelManager>(AiChannelManager(), permanent: true);
      LogUtil.debug('注册服务: AiChannelManager');
    }

    // 注册ChatManager
    if (!Get.isRegistered<ChatManager>()) {
      Get.put<ChatManager>(ChatManager(
        roleService: Get.find<RoleProvider>(),
      ), permanent: true);
      LogUtil.debug('注册服务: ChatManager');
    }

    // AiMessageProcessor已移除，其功能已合并到MessageService中

    // 注册MessageService
    if (!Get.isRegistered<MessageService>()) {
      // 尝试获取ISessionProvider实现
      ISessionProvider? sessionProvider;
      if (Get.isRegistered<ISessionProvider>()) {
        sessionProvider = Get.find<ISessionProvider>();
        LogUtil.debug('找到ISessionProvider实现，将注入到MessageService');
      } else {
        LogUtil.warn('未找到ISessionProvider实现，MessageService将无法更新会话列表');
      }

      Get.lazyPut<MessageService>(() => MessageService(
        globalState: Get.find<GlobalState>(),
        chatManager: Get.find<ChatManager>(),
        repository: Get.find<ChatRepository>(),
        sessionProvider: sessionProvider,
      ), fenix: true);
      LogUtil.debug('注册服务: MessageService');
    }

    // 注册ChatService - 确保替换默认的IChatService实现
    final chatService = ChatService(
      globalState: Get.find<GlobalState>(),
      chatManager: Get.find<ChatManager>(),
      messageService: Get.find<MessageService>(),
      repository: Get.find<ChatRepository>(),
      sessionProvider: Get.isRegistered<ISessionProvider>() ? Get.find<ISessionProvider>() : null,
    );

    // 使用ServiceBindings安全地替换服务
    ServiceBindings.replaceService<ChatService>(chatService);
    ServiceBindings.replaceService<IChatService>(chatService);
    LogUtil.debug('注册服务: ChatService 和接口: IChatService');

    // 注册举报服务
    if (!Get.isRegistered<ReportService>()) {
      Get.lazyPut<ReportService>(() => ReportService(
        repository: Get.find<ReportRepository>(),
      ), fenix: true);
      LogUtil.debug('注册服务: ReportService');
    }
  }

  void _registerControllers() {
    // 如果已经存在ChatController，先移除它
    if (Get.isRegistered<ChatController>()) {
      LogUtil.debug('移除已存在的ChatController');
      Get.delete<ChatController>(force: true);
    }

    Get.lazyPut<ChatController>(() => ChatController(
      chatService: Get.find<ChatService>(),
      globalState: Get.find<GlobalState>(),
    ));
    LogUtil.debug('注册控制器: ChatController');

    // 注册举报控制器
    if (!Get.isRegistered<ReportController>()) {
      Get.lazyPut<ReportController>(() => ReportController(
        reportService: Get.find<ReportService>(),
      ));
      LogUtil.debug('注册控制器: ReportController');
    }
  }

  /// 清理所有聊天相关的持久化缓存
  void _cleanupChatPersistentCache() async {
    try {
      final cacheManager = CacheManager.getInstance();
      
      // 获取所有缓存键
      final allKeys = await cacheManager.getKeys(strategy: CacheStrategy.both);
      
      // 过滤出聊天历史相关的缓存键
      final chatHistoryKeys = allKeys.where((key) => key.startsWith('chat_history_')).toList();
      
      // 移除所有聊天历史缓存
      if (chatHistoryKeys.isNotEmpty) {
        for (final key in chatHistoryKeys) {
          await cacheManager.remove(key, strategy: CacheStrategy.both);
        }
        
        LogUtil.info('已清理${chatHistoryKeys.length}个聊天历史缓存');
      }
    } catch (e) {
      // 如果发生错误，记录但不中断启动流程
      LogUtil.error('清理聊天持久化缓存失败: $e');
    }
  }
}