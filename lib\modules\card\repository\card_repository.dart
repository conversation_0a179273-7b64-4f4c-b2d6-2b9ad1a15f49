import 'dart:convert';
import 'dart:math';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/common/utils/error_handler.dart';
import 'package:rolio/common/constants/error_codes.dart';
import '../model/card.dart';
import '../model/card_rarity.dart';
import '../model/gacha_result.dart';

/// Card repository interface
abstract class ICardRepository {
  /// Get user's owned cards
  Future<List<Card>> getUserCards();
  
  /// Perform gacha pull
  Future<GachaResult> performGacha({
    required String pullType,
    required int cost,
    int count = 1,
  });
  
  /// Get available cards for gacha
  Future<List<Card>> getAvailableCards();
  
  /// Add card to user collection
  Future<bool> addCardToCollection(Card card);
  
  /// Remove card from user collection
  Future<bool> removeCardFromCollection(String cardId);
  
  /// Clear user card collection
  Future<bool> clearUserCards();
}

/// Local card repository implementation
class LocalCardRepository implements ICardRepository {
  static const String _userCardsKey = 'user_cards';
  static const String _availableCardsKey = 'available_cards';
  
  SharedPreferences? _prefs;
  final Uuid _uuid = const Uuid();
  final Random _random = Random();

  /// Ensure SharedPreferences is initialized
  Future<void> _ensureInitialized() async {
    _prefs ??= await SharedPreferences.getInstance();
  }

  @override
  Future<List<Card>> getUserCards() async {
    try {
      await _ensureInitialized();
      
      final cardsJson = _prefs!.getString(_userCardsKey);
      if (cardsJson == null) {
        LogUtil.debug('LocalCardRepository: 用户卡片数据不存在，返回空列表');
        return [];
      }
      
      final cardsList = json.decode(cardsJson) as List<dynamic>;
      final cards = cardsList.map((cardMap) => Card.fromMap(cardMap)).toList();
      
      LogUtil.debug('LocalCardRepository: 获取用户卡片成功，数量: ${cards.length}');
      return cards;
    } catch (e) {
      LogUtil.error('LocalCardRepository: 获取用户卡片失败: $e');
      return [];
    }
  }

  @override
  Future<List<Card>> getAvailableCards() async {
    try {
      await _ensureInitialized();
      
      // Check if we have cached available cards
      final cardsJson = _prefs!.getString(_availableCardsKey);
      if (cardsJson != null) {
        final cardsList = json.decode(cardsJson) as List<dynamic>;
        final cards = cardsList.map((cardMap) => Card.fromMap(cardMap)).toList();
        LogUtil.debug('LocalCardRepository: 获取可用卡片成功，数量: ${cards.length}');
        return cards;
      }
      
      // Generate default available cards if none exist
      final defaultCards = _generateDefaultCards();
      await _saveAvailableCards(defaultCards);
      
      LogUtil.info('LocalCardRepository: 生成默认卡片，数量: ${defaultCards.length}');
      return defaultCards;
    } catch (e) {
      LogUtil.error('LocalCardRepository: 获取可用卡片失败: $e');
      return _generateDefaultCards();
    }
  }

  @override
  Future<GachaResult> performGacha({
    required String pullType,
    required int cost,
    int count = 1,
  }) async {
    try {
      LogUtil.debug('LocalCardRepository: 执行抽卡，类型: $pullType，数量: $count');
      
      final availableCards = await getAvailableCards();
      if (availableCards.isEmpty) {
        throw Exception('No available cards for gacha');
      }
      
      final pulledCards = <Card>[];
      
      for (int i = 0; i < count; i++) {
        final card = _pullRandomCard(availableCards);
        pulledCards.add(card);
      }
      
      // Add pulled cards to user collection
      for (final card in pulledCards) {
        await addCardToCollection(card);
      }
      
      final result = GachaResult(
        cards: pulledCards,
        totalCost: cost,
        remainingBalance: 0, // This will be updated by the service
        timestamp: DateTime.now(),
        pullType: pullType,
      );
      
      LogUtil.info('LocalCardRepository: 抽卡完成，获得卡片: ${pulledCards.length}');
      return result;
    } catch (e) {
      LogUtil.error('LocalCardRepository: 抽卡失败: $e');
      rethrow;
    }
  }

  @override
  Future<bool> addCardToCollection(Card card) async {
    try {
      await _ensureInitialized();
      
      final userCards = await getUserCards();
      
      // Check if card already exists
      final existingCardIndex = userCards.indexWhere((c) => c.id == card.id);
      if (existingCardIndex != -1) {
        // Update owned count
        userCards[existingCardIndex] = userCards[existingCardIndex].copyWith(
          ownedCount: userCards[existingCardIndex].ownedCount + 1,
          isOwned: true,
        );
      } else {
        // Add new card
        userCards.add(card.copyWith(isOwned: true, ownedCount: 1));
      }
      
      final cardsJson = json.encode(userCards.map((c) => c.toMap()).toList());
      final success = await _prefs!.setString(_userCardsKey, cardsJson);
      
      if (success) {
        LogUtil.debug('LocalCardRepository: 添加卡片到收藏成功: ${card.name}');
      }
      
      return success;
    } catch (e) {
      LogUtil.error('LocalCardRepository: 添加卡片到收藏失败: $e');
      return false;
    }
  }

  @override
  Future<bool> removeCardFromCollection(String cardId) async {
    try {
      await _ensureInitialized();
      
      final userCards = await getUserCards();
      userCards.removeWhere((card) => card.id == cardId);
      
      final cardsJson = json.encode(userCards.map((c) => c.toMap()).toList());
      final success = await _prefs!.setString(_userCardsKey, cardsJson);
      
      if (success) {
        LogUtil.debug('LocalCardRepository: 从收藏移除卡片成功: $cardId');
      }
      
      return success;
    } catch (e) {
      LogUtil.error('LocalCardRepository: 从收藏移除卡片失败: $e');
      return false;
    }
  }

  @override
  Future<bool> clearUserCards() async {
    try {
      await _ensureInitialized();
      
      final success = await _prefs!.remove(_userCardsKey);
      
      if (success) {
        LogUtil.info('LocalCardRepository: 清空用户卡片收藏成功');
      }
      
      return success;
    } catch (e) {
      LogUtil.error('LocalCardRepository: 清空用户卡片收藏失败: $e');
      return false;
    }
  }

  /// Save available cards to cache
  Future<void> _saveAvailableCards(List<Card> cards) async {
    try {
      await _ensureInitialized();
      final cardsJson = json.encode(cards.map((c) => c.toMap()).toList());
      await _prefs!.setString(_availableCardsKey, cardsJson);
    } catch (e) {
      LogUtil.error('LocalCardRepository: 保存可用卡片失败: $e');
    }
  }

  /// Generate default cards for testing
  List<Card> _generateDefaultCards() {
    final cards = <Card>[];
    final roles = [
      {'id': '1', 'name': 'Alice'},
      {'id': '2', 'name': 'Bob'},
      {'id': '3', 'name': 'Charlie'},
      {'id': '4', 'name': 'Diana'},
      {'id': '5', 'name': 'Eve'},
    ];
    
    for (final role in roles) {
      for (final rarity in CardRarity.values) {
        cards.add(Card(
          id: _uuid.v4(),
          name: '${role['name']} ${rarity.displayName} Card',
          description: 'A ${rarity.displayName.toLowerCase()} card featuring ${role['name']}',
          imageUrl: 'https://via.placeholder.com/300x400?text=${role['name']}+${rarity.displayName}',
          rarity: rarity,
          roleId: role['id']!,
          roleName: role['name']!,
          createdAt: DateTime.now(),
        ));
      }
    }
    
    return cards;
  }

  /// Pull a random card based on rarity probabilities
  Card _pullRandomCard(List<Card> availableCards) {
    final randomValue = _random.nextInt(10000);
    int cumulativeProbability = 0;
    
    // Sort rarities by probability (highest first for cumulative calculation)
    final sortedRarities = CardRarity.values.toList()
      ..sort((a, b) => b.probability.compareTo(a.probability));
    
    for (final rarity in sortedRarities) {
      cumulativeProbability += rarity.probability;
      if (randomValue < cumulativeProbability) {
        final cardsOfRarity = availableCards.where((card) => card.rarity == rarity).toList();
        if (cardsOfRarity.isNotEmpty) {
          return cardsOfRarity[_random.nextInt(cardsOfRarity.length)];
        }
      }
    }
    
    // Fallback to common rarity
    final commonCards = availableCards.where((card) => card.rarity == CardRarity.common).toList();
    return commonCards.isNotEmpty 
        ? commonCards[_random.nextInt(commonCards.length)]
        : availableCards[_random.nextInt(availableCards.length)];
  }
}

/// API card repository implementation (placeholder)
class ApiCardRepository implements ICardRepository {
  @override
  Future<List<Card>> getUserCards() async {
    // TODO: Implement API call
    throw UnimplementedError('API card repository not implemented');
  }

  @override
  Future<GachaResult> performGacha({
    required String pullType,
    required int cost,
    int count = 1,
  }) async {
    // TODO: Implement API call
    throw UnimplementedError('API card repository not implemented');
  }

  @override
  Future<List<Card>> getAvailableCards() async {
    // TODO: Implement API call
    throw UnimplementedError('API card repository not implemented');
  }

  @override
  Future<bool> addCardToCollection(Card card) async {
    // TODO: Implement API call
    throw UnimplementedError('API card repository not implemented');
  }

  @override
  Future<bool> removeCardFromCollection(String cardId) async {
    // TODO: Implement API call
    throw UnimplementedError('API card repository not implemented');
  }

  @override
  Future<bool> clearUserCards() async {
    // TODO: Implement API call
    throw UnimplementedError('API card repository not implemented');
  }
}
