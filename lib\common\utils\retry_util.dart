import 'dart:async';
import 'dart:math';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/common/utils/error_handler.dart';
import 'package:rolio/common/constants/error_codes.dart';

/// 重试配置类
class RetryConfig {
  final int maxRetries;
  final int baseDelayMs;
  final int maxDelayMs;
  final bool enableNetworkCheck;
  final bool enableJitter;

  const RetryConfig({
    this.maxRetries = RetryUtil.defaultMaxRetries,
    this.baseDelayMs = RetryUtil.defaultBaseDelayMs,
    this.maxDelayMs = RetryUtil.defaultMaxDelayMs,
    this.enableNetworkCheck = true,
    this.enableJitter = true,
  });
}

/// 重试状态类
class RetryState {
  final int currentAttempt;
  final int maxAttempts;
  final int delayMs;
  final bool networkAvailable;
  final String? lastError;

  const RetryState({
    required this.currentAttempt,
    required this.maxAttempts,
    required this.delayMs,
    required this.networkAvailable,
    this.lastError,
  });

  bool get canRetry => currentAttempt < maxAttempts && networkAvailable;
  bool get isLastAttempt => currentAttempt >= maxAttempts;
}

/// 通用重试工具类
///
/// 提供指数退避、网络状态检测和智能重试条件判断功能
/// 借鉴WebSocket连接器和HttpManager中的成熟实现
class RetryUtil {
  // 默认配置
  static const int defaultMaxRetries = 3;
  static const int defaultBaseDelayMs = 300;
  static const int defaultMaxDelayMs = 30000;
  
  /// 计算重试延迟时间（毫秒）
  /// 
  /// 使用指数退避算法，借鉴WebSocket连接器的实现
  /// [retryCount] 当前重试次数（从0开始）
  /// [baseDelay] 基础延迟时间（毫秒）
  /// [maxDelay] 最大延迟时间（毫秒）
  /// [enableJitter] 是否启用随机抖动
  static int calculateRetryDelay(
    int retryCount, 
    int baseDelay, 
    int maxDelay, {
    bool enableJitter = true,
  }) {
    // 使用指数退避算法计算延迟
    final delay = min(baseDelay * pow(2, retryCount), maxDelay).toInt();
    
    if (!enableJitter) {
      return delay;
    }
    
    // 添加随机抖动，避免惊群效应
    final jitter = Random().nextInt(delay ~/ 4);
    return delay + jitter;
  }
  
  /// 检查当前网络是否可用
  /// 
  /// 复用WebSocket连接器中的网络检测逻辑
  static Future<bool> isNetworkConnected() async {
    try {
      final connectivityResult = await Connectivity().checkConnectivity();
      final isConnected = connectivityResult.isNotEmpty && 
                          !connectivityResult.contains(ConnectivityResult.none);
      LogUtil.debug('RetryUtil: 网络连接检查结果: $connectivityResult, 网络可用: $isConnected');
      return isConnected;
    } catch (e) {
      LogUtil.error('RetryUtil: 检查网络连接状态失败: $e');
      // 默认返回true，避免因为检查失败而阻止重试
      return true;
    }
  }
  
  /// 判断错误是否可重试
  /// 
  /// 参考HttpManager和PaginationMixin中的错误判断逻辑
  /// [error] 错误对象
  static bool shouldRetry(dynamic error) {
    if (error == null) return false;
    
    final errorString = error.toString().toLowerCase();
    
    // 网络相关错误通常可以重试
    if (errorString.contains('network') ||
        errorString.contains('timeout') ||
        errorString.contains('connection') ||
        errorString.contains('socket') ||
        errorString.contains('unreachable')) {
      return true;
    }
    
    // 服务器5xx错误可以重试
    if (errorString.contains('500') ||
        errorString.contains('502') ||
        errorString.contains('503') ||
        errorString.contains('504')) {
      return true;
    }
    
    // HTTP连接相关错误
    if (errorString.contains('connectiontimeout') ||
        errorString.contains('sendtimeout') ||
        errorString.contains('receivetimeout') ||
        errorString.contains('connectionerror')) {
      return true;
    }
    
    // 其他错误不重试（如4xx客户端错误）
    return false;
  }
  
  /// 执行带重试的异步操作
  ///
  /// [operation] 要执行的异步操作
  /// [config] 重试配置
  /// [onRetry] 重试回调函数，可用于更新UI状态
  /// [operationName] 操作名称，用于日志记录
  static Future<T> executeWithRetry<T>(
    Future<T> Function() operation, {
    RetryConfig config = const RetryConfig(),
    Function(RetryState state)? onRetry,
    String operationName = 'Unknown Operation',
  }) async {
    int currentAttempt = 0;
    dynamic lastError;
    
    while (currentAttempt <= config.maxRetries) {
      try {
        // 检查网络状态（除了第一次尝试）
        bool networkAvailable = true;
        if (config.enableNetworkCheck && currentAttempt > 0) {
          networkAvailable = await isNetworkConnected();
          if (!networkAvailable) {
            LogUtil.warn('RetryUtil: 网络不可用，停止重试 $operationName');
            throw NetworkException(
              'Network is not available',
              code: ErrorCodes.NETWORK_ERROR
            );
          }
        }
        
        LogUtil.debug('RetryUtil: 执行操作 $operationName，尝试次数: ${currentAttempt + 1}/${config.maxRetries + 1}');
        
        // 执行操作
        final result = await operation();
        
        // 成功时记录日志
        if (currentAttempt > 0) {
          LogUtil.info('RetryUtil: 操作 $operationName 在第${currentAttempt + 1}次尝试后成功');
        }
        
        return result;
        
      } catch (e) {
        lastError = e;
        currentAttempt++;
        
        LogUtil.error('RetryUtil: 操作 $operationName 第$currentAttempt次尝试失败: $e');
        
        // 如果已达到最大重试次数，抛出最后的错误
        if (currentAttempt > config.maxRetries) {
          LogUtil.error('RetryUtil: 操作 $operationName 已达到最大重试次数(${config.maxRetries})，停止重试');
          rethrow;
        }
        
        // 检查错误是否可重试
        if (!shouldRetry(e)) {
          LogUtil.warn('RetryUtil: 操作 $operationName 遇到不可重试的错误，停止重试: $e');
          rethrow;
        }
        
        // 计算延迟时间
        final delayMs = calculateRetryDelay(
          currentAttempt - 1, 
          config.baseDelayMs, 
          config.maxDelayMs,
          enableJitter: config.enableJitter,
        );
        
        // 创建重试状态
        final retryState = RetryState(
          currentAttempt: currentAttempt,
          maxAttempts: config.maxRetries,
          delayMs: delayMs,
          networkAvailable: true, // 此时网络应该是可用的
          lastError: e.toString(),
        );
        
        // 调用重试回调
        onRetry?.call(retryState);
        
        LogUtil.info('RetryUtil: 操作 $operationName 将在${delayMs}ms后进行第${currentAttempt + 1}次重试');
        
        // 延迟后重试
        await Future.delayed(Duration(milliseconds: delayMs));
      }
    }
    
    // 理论上不会到达这里，但为了类型安全
    throw lastError ?? Exception('Unknown error in retry operation');
  }
}


