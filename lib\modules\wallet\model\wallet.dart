/// 钱包模型
class Wallet {
  /// 钻石余额
  final int diamonds;
  
  /// 最后更新时间
  final DateTime lastUpdated;
  
  /// 预留扩展字段
  final Map<String, dynamic>? metadata;

  /// 构造函数
  const Wallet({
    required this.diamonds,
    required this.lastUpdated,
    this.metadata,
  });

  /// 将钱包数据转换为Map
  Map<String, dynamic> toMap() {
    return {
      'diamonds': diamonds,
      'lastUpdated': lastUpdated.millisecondsSinceEpoch,
      'metadata': metadata,
    };
  }

  /// 从Map创建钱包对象
  factory Wallet.fromMap(Map<String, dynamic> map) {
    return Wallet(
      diamonds: map['diamonds'] as int? ?? 0,
      lastUpdated: DateTime.fromMillisecondsSinceEpoch(
        map['lastUpdated'] as int? ?? DateTime.now().millisecondsSinceEpoch,
      ),
      metadata: map['metadata'] as Map<String, dynamic>?,
    );
  }

  /// 创建副本
  Wallet copyWith({
    int? diamonds,
    DateTime? lastUpdated,
    Map<String, dynamic>? metadata,
  }) {
    return Wallet(
      diamonds: diamonds ?? this.diamonds,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      metadata: metadata ?? this.metadata,
    );
  }

  /// 检查是否有足够的钻石
  bool hasEnoughDiamonds(int amount) {
    return diamonds >= amount;
  }

  /// 扣除钻石后的新钱包对象
  Wallet spendDiamonds(int amount) {
    if (!hasEnoughDiamonds(amount)) {
      throw Exception('Insufficient diamonds: need $amount, have $diamonds');
    }
    return copyWith(
      diamonds: diamonds - amount,
      lastUpdated: DateTime.now(),
    );
  }

  /// 增加钻石后的新钱包对象
  Wallet addDiamonds(int amount) {
    return copyWith(
      diamonds: diamonds + amount,
      lastUpdated: DateTime.now(),
    );
  }

  @override
  String toString() {
    return 'Wallet(diamonds: $diamonds, lastUpdated: $lastUpdated, metadata: $metadata)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Wallet &&
        other.diamonds == diamonds &&
        other.lastUpdated == lastUpdated;
  }

  @override
  int get hashCode {
    return diamonds.hashCode ^ lastUpdated.hashCode;
  }
}
