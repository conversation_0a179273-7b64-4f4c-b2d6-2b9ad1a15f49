import 'package:get/get.dart';
import 'package:rolio/common/models/ai_role.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/common/utils/toast_util.dart';
import 'package:rolio/common/utils/pagination_mixin.dart';
import 'package:rolio/common/models/page_request.dart';
import 'package:rolio/modules/role/repository/search_repository.dart';
import 'package:rolio/modules/role/service/search_service.dart';
import 'package:flutter/material.dart';
import 'dart:async';

/// 搜索状态枚举
enum SearchState {
  idle,        // 空闲状态
  searching,   // 正在搜索
  suggestions, // 正在获取建议
  loadingMore, // 正在加载更多
}

/// 搜索控制器
///
/// 负责管理搜索页面的UI状态和业务逻辑
class SearchController extends GetxController with PaginationMixin {
  // 服务
  late final SearchService searchService;

  // 搜索关键词
  final RxString searchKeyword = ''.obs;

  // 搜索结果
  final RxList<AiRole> searchResults = <AiRole>[].obs;

  // 统一的搜索状态
  final Rx<SearchState> searchState = SearchState.idle.obs;

  // 便捷的状态访问器
  bool get isSearching => searchState.value == SearchState.searching;
  bool get isWaitingSuggestions => searchState.value == SearchState.suggestions;
  bool get isLoadingMoreResults => searchState.value == SearchState.loadingMore;
  
  // 搜索历史
  final RxList<SearchRecord> searchHistory = <SearchRecord>[].obs;
  
  // 搜索建议
  final RxList<String> searchSuggestions = <String>[].obs;
  
  // 随机角色名(用作提示)
  final RxString randomRoleName = ''.obs;
  
  // 编辑模式(删除历史记录)
  final RxBool isEditMode = false.obs;
  
  // 分页信息
  final RxInt currentPage = 1.obs;
  final RxInt totalPages = 1.obs;
  final RxInt totalItems = 0.obs;
  
  // 滚动控制器
  final ScrollController scrollController = ScrollController();
  
  // 搜索框控制器
  final TextEditingController textEditingController = TextEditingController();
  
  // 搜索框焦点
  final FocusNode searchFocusNode = FocusNode();
  
  // 搜索建议延迟Worker - 使用GetX
  Worker? _suggestionsWorker;

  // 资源清理状态标记
  bool _disposed = false;

  @override
  void onInit() {
    super.onInit();
    searchService = Get.find<SearchService>();

    // 初始化分页状态
    initializePagination(
      initialRequest: const PageRequest(page: 1, size: 20),
      hasMore: true,
    );

    // 加载搜索历史
    _loadSearchHistory();

    // 加载随机角色名
    _loadRandomRoleName();
    
    // 监听滚动，实现滚动到底部自动加载更多
    scrollController.addListener(_scrollListener);
    
    // 监听搜索框文本变化
    textEditingController.addListener(_onSearchTextChanged);
  }
  
  @override
  void onClose() {
    // 防止重复清理
    if (_disposed) {
      LogUtil.debug('SearchController: 资源已清理，跳过重复清理');
      return;
    }
    _disposed = true;

    try {
      // 释放资源
      scrollController.removeListener(_scrollListener);
      scrollController.dispose();
      textEditingController.removeListener(_onSearchTextChanged);
      textEditingController.dispose();
      searchFocusNode.dispose();
      searchService.cancelDebounce();

      // 安全清理Worker
      if (_suggestionsWorker != null) {
        try {
          _suggestionsWorker!.dispose();
          _suggestionsWorker = null;
        } catch (e) {
          LogUtil.error('SearchController: Worker清理失败: $e');
        }
      }

      // 清理分页资源
      disposePagination();

      LogUtil.debug('SearchController: 资源清理完成');
    } catch (e) {
      LogUtil.error('SearchController: 资源清理过程中发生错误: $e');
    } finally {
      super.onClose();
    }
  }
  
  /// 监听搜索框文本变化
  void _onSearchTextChanged() {
    final text = textEditingController.text;
    
    // 更新关键词
    searchKeyword.value = text;
    
    // 如果文本为空，清除搜索结果和建议
    if (text.isEmpty) {
      searchResults.clear();
      searchSuggestions.clear();
      searchState.value = SearchState.idle;
      _suggestionsWorker?.dispose();
      return;
    }

    // 设置等待搜索建议状态为true（一个字符时也显示加载状态）
    if (text.trim().isNotEmpty) {
      searchState.value = SearchState.suggestions;

      // 安全取消之前的Worker
      if (_suggestionsWorker != null) {
        try {
          _suggestionsWorker!.dispose();
          _suggestionsWorker = null;
        } catch (e) {
          LogUtil.error('SearchController: 取消之前的Worker失败: $e');
        }
      }

      // 创建超时触发器，延迟800ms后如果还没有建议就隐藏加载状态
      final timeoutTrigger = 0.obs;
      _suggestionsWorker = ever(timeoutTrigger, (_) {
        if (!_disposed) {
          searchState.value = SearchState.idle;
        }
        // 清理临时响应式变量
        timeoutTrigger.close();
      });

      // 使用Future.delayed触发超时，时间与Service层防抖时间一致
      Future.delayed(const Duration(milliseconds: 800), () {
        if (!_disposed) {
          timeoutTrigger.value++;
        }
      });
    } else {
      // 关键词为空，直接清除建议和加载状态
      searchSuggestions.clear();
      searchState.value = SearchState.idle;
      if (_suggestionsWorker != null) {
        try {
          _suggestionsWorker!.dispose();
          _suggestionsWorker = null;
        } catch (e) {
          LogUtil.error('SearchController: 清理Worker失败: $e');
        }
      }
      return;
    }

    // 获取搜索建议
    searchService.getSearchSuggestions(text, callback: (suggestions) {
      // 如果用户已经继续输入，不更新建议
      if (textEditingController.text != text) {
        LogUtil.debug('SearchController: 用户已继续输入，忽略过期的搜索建议');
        return;
      }

      // 更新搜索建议
      if (!_disposed) {
        searchSuggestions.value = suggestions;
        searchState.value = SearchState.idle; // 已获取建议，不再等待
      }

      // 安全取消超时Worker
      if (_suggestionsWorker != null) {
        try {
          _suggestionsWorker!.dispose();
          _suggestionsWorker = null;
        } catch (e) {
          LogUtil.error('SearchController: 取消超时Worker失败: $e');
        }
      }

      LogUtil.debug('SearchController: 更新搜索建议，共${suggestions.length}条');
    });
  }
  
  /// 监听滚动事件
  void _scrollListener() {
    // 如果已经到达底部，且不是正在加载中，则加载更多
    if (scrollController.position.pixels >= scrollController.position.maxScrollExtent - 200 &&
        !isSearching &&
        !isLoadingMore.value &&
        currentPage.value < totalPages.value) {
      loadMore();
    }
  }
  
  /// 执行搜索
  Future<void> search(String keyword) async {
    if (keyword.trim().isEmpty) {
      // 如果关键词为空但有随机角色名，则搜索随机角色
      if (randomRoleName.isNotEmpty) {
        keyword = randomRoleName.value;
        textEditingController.text = keyword;
      } else {
        return;
      }
    }
    
    try {
      searchState.value = SearchState.searching;
      searchKeyword.value = keyword;

      LogUtil.debug('搜索角色: $keyword');

      final result = await searchService.searchRoles(
        keyword: keyword,
        page: 1,
        pageSize: 20,
      );

      // 更新搜索结果
      searchResults.clear();
      if (result['items'] is List) {
        searchResults.addAll(result['items'] as List<AiRole>);
      }

      // 更新分页信息
      currentPage.value = result['page'] ?? 1;
      totalPages.value = result['pages'] ?? 1;
      totalItems.value = result['total'] ?? 0;

      // 更新分页状态
      updateHasMoreData(currentPage.value < totalPages.value);

      LogUtil.debug('搜索结果: ${searchResults.length}个角色, 总共: ${totalItems.value}个');

      // 退出编辑模式
      isEditMode.value = false;

      // 隐藏搜索建议
      searchSuggestions.clear();

      // 重新加载搜索历史
      _loadSearchHistory();
    } catch (e) {
      LogUtil.error('搜索角色失败: $e');
      ToastUtil.error('Failed to search roles');
    } finally {
      searchState.value = SearchState.idle;
    }
  }
  
  /// 加载更多搜索结果 - 使用PaginationMixin
  Future<void> loadMore() async {
    if (isSearching || searchKeyword.isEmpty) {
      return;
    }

    searchState.value = SearchState.loadingMore;
    await handleLoadMore(_loadMoreSearchResultsImpl);
  }

  /// 实际的加载更多搜索结果逻辑实现
  Future<bool> _loadMoreSearchResultsImpl(PageRequest request) async {
    try {
      final currentResultMap = {
        'items': searchResults.toList(),
        'page': currentPage.value,
        'pages': totalPages.value,
        'total': totalItems.value,
        'size': request.size,
      };

      LogUtil.debug('加载更多搜索结果，页码: ${request.page}');

      final result = await searchService.loadNextPage(
        currentResultMap,
        keyword: searchKeyword.value,
      );

      // 更新搜索结果
      searchResults.clear();
      if (result['items'] is List) {
        searchResults.addAll(result['items'] as List<AiRole>);
      }

      // 更新分页信息
      currentPage.value = result['page'] ?? currentPage.value;
      totalPages.value = result['pages'] ?? totalPages.value;
      totalItems.value = result['total'] ?? totalItems.value;

      // 更新分页状态
      updateHasMoreData(currentPage.value < totalPages.value);

      LogUtil.debug('加载更多搜索结果完成，当前结果数: ${searchResults.length}');
      return true;

    } catch (e) {
      LogUtil.error('加载更多搜索结果失败: $e');
      return false;
    } finally {
      searchState.value = SearchState.idle;
    }
  }
  
  /// 清除搜索结果
  void clearSearch() {
    searchKeyword.value = '';
    textEditingController.clear();
    searchResults.clear();
    searchSuggestions.clear();
    searchState.value = SearchState.idle;
    currentPage.value = 1;
    totalPages.value = 1;
    totalItems.value = 0;

    // 安全清理Worker
    if (_suggestionsWorker != null) {
      try {
        _suggestionsWorker!.dispose();
        _suggestionsWorker = null;
      } catch (e) {
        LogUtil.error('SearchController: 清理Worker失败: $e');
      }
    }

    // 退出编辑模式
    isEditMode.value = false;

    // 重新加载随机角色名
    _loadRandomRoleName();
  }
  
  /// 从搜索历史中执行搜索
  void searchFromHistory(SearchRecord record) {
    textEditingController.text = record.keyword;
    search(record.keyword);
  }
  
  /// 获取搜索缓存统计信息
  Map<String, dynamic> getCacheStats() {
    return searchService.getCacheStats();
  }
  
  /// 清除搜索缓存
  /// 
  /// [type] 缓存类型：'result', 'suggestions', 'history', 'all'
  Future<bool> clearSearchCache(String type) async {
    return await searchService.clearCache(type);
  }
  
  /// 维护搜索缓存
  /// 
  /// 定期清理过期缓存并优化缓存大小
  Future<void> maintainSearchCache() async {
    await searchService.maintainCache();
  }
  

  
  /// 从搜索建议中执行搜索
  void searchFromSuggestion(String suggestion) {
    textEditingController.text = suggestion;
    search(suggestion);
  }
  
  /// 切换编辑模式
  void toggleEditMode() {
    isEditMode.value = !isEditMode.value;
  }
  
  /// 删除单个搜索历史
  Future<void> deleteSearchRecord(int recordId) async {
    try {
      final success = await searchService.deleteSearchRecord(recordId);
      if (success) {
        // 移除本地列表中的记录
        searchHistory.removeWhere((record) => record.id == recordId);
      } else {
        LogUtil.warn('删除搜索记录失败: $recordId');
        ToastUtil.error('Failed to delete search history');
      }
    } catch (e) {
      LogUtil.error('删除搜索记录失败: $e');
    }
  }
  
  /// 清除所有搜索历史（带确认框）
  Future<void> clearAllSearchHistoryWithConfirmation() async {
    // 显示确认对话框
    final bool? confirmed = await Get.dialog<bool>(
      AlertDialog(
        backgroundColor: Colors.grey.shade900,
        title: const Text(
          'Clear All History',
          style: TextStyle(color: Colors.white),
        ),
        content: const Text(
          'Are you sure you want to clear all search history? This action cannot be undone.',
          style: TextStyle(color: Colors.white70),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: const Text(
              'Cancel',
              style: TextStyle(color: Colors.white70),
            ),
          ),
          TextButton(
            onPressed: () => Get.back(result: true),
            child: const Text(
              'Clear All',
              style: TextStyle(color: Colors.red),
            ),
          ),
        ],
      ),
    );

    // 如果用户确认，则执行删除
    if (confirmed == true) {
      await clearAllSearchHistory();
    }
  }

  /// 清除所有搜索历史（内部方法）
  Future<void> clearAllSearchHistory() async {
    try {
      final deletedCount = await searchService.deleteAllSearchRecords();
      LogUtil.debug('删除搜索历史: $deletedCount 条记录');

      // 清空本地列表
      searchHistory.clear();

      // 退出编辑模式
      isEditMode.value = false;

      ToastUtil.success('All search history cleared');
    } catch (e) {
      LogUtil.error('清除所有搜索历史失败: $e');
      ToastUtil.error('Failed to clear search history');
    }
  }
  
  /// 加载搜索历史
  Future<void> _loadSearchHistory() async {
    try {
      final histories = await searchService.getSearchHistory(size: 20, forceRefresh: true);
      searchHistory.value = histories;
    } catch (e) {
      LogUtil.error('加载搜索历史失败: $e');
    }
  }
  
  /// 加载随机角色名
  Future<void> _loadRandomRoleName() async {
    try {
      final name = await searchService.getRandomRoleName();
      randomRoleName.value = name;
    } catch (e) {
      LogUtil.error('加载随机角色名失败: $e');
    }
  }
} 