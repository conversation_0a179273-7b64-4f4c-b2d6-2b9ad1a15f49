import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/routes/router_manager.dart';
import 'package:rolio/routes/routes.dart';
import '../controller/user_controller.dart';
import 'dart:async'; // Added for Timer
import 'package:rolio/modules/user/view/avatar_selector_dialog.dart';
import 'package:rolio/common/utils/toast_util.dart';

/// 用户页面
class UserPage extends GetView<UserController> {
  /// 构造函数
  const UserPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: RefreshIndicator(
        onRefresh: () async {
          await controller.refreshUserInfo();
        },
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: Column(
            children: [
              // 用户信息区域
              _buildUserInfoSection(),
              
              // 功能区域 - 包括所有功能按钮
              _buildFunctionalButtonsArea(),
              
              // 空白填充
              const Padding(
                padding: EdgeInsets.symmetric(vertical: 20),
              ),
              
              // 登录/登出按钮移到底部
              _buildLoginLogoutButton(),
              
              // 底部安全间距
              const SizedBox(height: 32),
            ],
          ),
        ),
      ),
    );
  }
  
  /// 用户信息区域
  Widget _buildUserInfoSection() {
    return Obx(() {
      final isGuest = controller.isGuest.value;
      final photoUrl = controller.user.value?.photoURL;
      final userName = controller.user.value?.userName ?? '';
      
      return Container(
        width: double.infinity,
        padding: const EdgeInsets.fromLTRB(20, 70, 20, 20),
        decoration: const BoxDecoration(
          color: Colors.black,
          border: Border(
            bottom: BorderSide(color: Colors.grey, width: 0.2),
          ),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // 头像
            GestureDetector(
              onTap: _showAvatarSelectorDialog,
              child: Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  shape: BoxShape.rectangle,
                  borderRadius: BorderRadius.circular(16),
                  color: Colors.grey.shade700,
                ),
                child: isGuest || photoUrl == null || photoUrl.isEmpty
                    ? Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [Colors.grey.shade600, Colors.grey.shade800],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                          borderRadius: BorderRadius.circular(16),
                        ),
                      )
                    : ClipRRect(
                        borderRadius: BorderRadius.circular(16),
                        child: Stack(
                          fit: StackFit.expand,
                          children: [
                            // 灰色背景和加载指示器作为占位符
                            Container(
                              color: Colors.grey.shade700,
                              child: const Center(
                                child: SizedBox(
                                  width: 30,
                                  height: 30,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    color: Colors.white38,
                                  ),
                                ),
                              ),
                            ),
                            // 网络图片
                            Image.network(
                              photoUrl,
                              fit: BoxFit.cover,
                              loadingBuilder: (context, child, loadingProgress) {
                                if (loadingProgress == null) {
                                  // 图片加载完成，使用淡入效果显示
                                  return AnimatedOpacity(
                                    opacity: 1.0,
                                    duration: const Duration(milliseconds: 500),
                                    child: child,
                                  );
                                }
                                // 正在加载时返回空容器，显示底层的加载指示器
                                return Container();
                              },
                              errorBuilder: (context, _, __) => Container(
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    colors: [Colors.grey.shade700, Colors.grey.shade900],
                                    begin: Alignment.topLeft,
                                    end: Alignment.bottomRight,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
              ),
            ),
            
            const SizedBox(width: 20),
            
            // 用户名
            Expanded(
              child: GestureDetector(
                onTap: isGuest ? null : _showEditNameDialog,
                child: Row(
                  children: [
                    Flexible(
                      child: Text(
                        isGuest ? "Not Logged In" : userName,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    if (!isGuest)
                      const Padding(
                        padding: EdgeInsets.only(left: 8.0),
                        child: Icon(Icons.edit, color: Colors.white54, size: 16),
                      ),
                  ],
                ),
              ),
            ),
          ],
        ),
      );
    });
  }
  
  /// 功能按钮区域 - 现在放在上方
  Widget _buildFunctionalButtonsArea() {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 20, 16, 0),
      child: Column(
        children: [
          _buildModernFunctionButton(
            icon: Icons.account_balance_wallet_outlined,
            title: 'Wallet',
            onTap: () {
              RouterManager.navigateTo(Routes.walletScreen);
            },
          ),
          _buildModernFunctionButton(
            icon: Icons.credit_card_outlined,
            title: 'My Cards',
            onTap: () {
              RouterManager.navigateTo(Routes.userCardsScreen);
            },
          ),

          _buildModernFunctionButton(
            icon: Icons.favorite_border,
            title: 'Favorite Characters',
            onTap: () {
              Get.toNamed(Routes.favoriteScreen);
            },
          ),
        ],
      ),
    );
  }
  
  /// 现代化功能按钮
  Widget _buildModernFunctionButton({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 1),
      child: Container(
        decoration: BoxDecoration(
          color: const Color(0xFF1A1A1A),
          borderRadius: BorderRadius.circular(10),
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: onTap,
            borderRadius: BorderRadius.circular(10),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Container(
                    width: 42,
                    height: 42,
                    decoration: BoxDecoration(
                      color: const Color(0xFF0A1A30),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Icon(
                      icon,
                      color: Colors.blue.shade300,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Text(
                      title,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 17,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  const Icon(
                    Icons.chevron_right,
                    color: Colors.white54,
                    size: 24,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
  
  /// 登录/登出按钮 - 现在放在底部
  Widget _buildLoginLogoutButton() {
    return Obx(() {
      final isGuest = controller.isGuest.value;
      
      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 32),
        child: Container(
          width: double.infinity,
          height: 52,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(26),
            gradient: isGuest 
              ? const LinearGradient(
                  colors: [Color(0xFF4776E6), Color(0xFF8E54E9)],
                  begin: Alignment.centerLeft,
                  end: Alignment.centerRight,
                )
              : const LinearGradient(
                  colors: [Color(0xFFFF416C), Color(0xFFFF4B2B)],
                  begin: Alignment.centerLeft,
                  end: Alignment.centerRight,
                ),
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: () {
                if (!isGuest) {
                  controller.logout();
                } else {
                  RouterManager.navigateTo(Routes.loginScreen);
                }
              },
              borderRadius: BorderRadius.circular(26),
              child: Center(
                child: Text(
                  isGuest ? "Login" : "Logout",
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ),
        ),
      );
    });
  }
  
  /// 显示头像选择对话框
  void _showAvatarSelectorDialog() {
    // 检查用户是否为游客
    if (controller.isGuest.value) {
      ToastUtil.info('Please login to change your avatar');
      return;
    }
    
    // 显示头像选择对话框，使用控制器
    Get.dialog(
      const AvatarSelectorDialog(),
      barrierDismissible: true,
    ).then((_) {
      // 对话框关闭后刷新用户信息
      controller.refreshUserInfo();
    });
  }
  
  /// 显示编辑用户名对话框
  void _showEditNameDialog() {
    final TextEditingController textController = TextEditingController();
    textController.text = controller.user.value?.userName ?? '';
    
    Get.dialog(
      AlertDialog(
        title: const Text('Edit Username', style: TextStyle(fontSize: 16, color: Colors.white)),
        content: Obx(() => controller.isUpdating.value
          ? const Center(
              child: CircularProgressIndicator(),
            )
          : TextField(
              controller: textController,
              decoration: const InputDecoration(
                hintText: 'Enter new username',
                hintStyle: TextStyle(color: Colors.grey),
                contentPadding: EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                border: OutlineInputBorder(),
                enabledBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: Colors.grey),
                ),
                helperText: '1-30 characters, letters, numbers, _.-',
                helperStyle: TextStyle(color: Colors.grey, fontSize: 12),
                counterText: '',
              ),
              maxLength: UserController.MAX_USERNAME_LENGTH,
              style: const TextStyle(fontSize: 14, color: Colors.white),
            ),
        ),
        actions: [
          TextButton(
            onPressed: controller.isUpdating.value ? null : () => Get.back(),
            child: const Text('Cancel', style: TextStyle(color: Colors.grey, fontSize: 14)),
          ),
          TextButton(
            onPressed: controller.isUpdating.value 
              ? null
              : () {
                  final newName = textController.text.trim();
                  if (newName.isNotEmpty) {
                    // 调用控制器的更新用户名方法
                    controller.updateUsername(newName);
                  }
                  Get.back();
                },
            child: const Text('Confirm', style: TextStyle(fontSize: 14)),
          ),
        ],
        backgroundColor: Colors.black,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }
}