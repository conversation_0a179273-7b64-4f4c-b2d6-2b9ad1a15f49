import 'dart:async';
import 'dart:math' as math;
import 'package:get/get.dart';
import 'package:rolio/common/constants/error_codes.dart';
import 'package:rolio/common/enums/message_type.dart';
import 'package:rolio/common/interfaces/chat_service_interface.dart';
import 'package:rolio/common/interfaces/session_provider.dart';
import 'package:rolio/common/models/ai_role.dart';
import 'package:rolio/common/services/role_provider.dart';
import 'package:rolio/common/utils/error_handler.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/common/state/global_event_state.dart';
import 'package:rolio/manager/global_state.dart';
import 'package:rolio/manager/ws_manager.dart';
import 'package:rolio/manager/ws_message_manager.dart';
import 'package:rolio/modules/chat/model/message.dart';
import 'package:rolio/modules/chat/repository/chat_repository.dart';
import 'package:rolio/modules/chat/service/chat_manager.dart';
import 'package:rolio/modules/chat/service/message_service.dart';
import 'package:rolio/common/cache/cache_manager.dart';
import 'package:rolio/common/services/session_binding_service.dart';
import 'package:rolio/modules/chat/service/ai_channel_manager.dart';

/// 聊天状态管理
///
/// 分离UI状态和业务状态，提高状态管理的清晰度和可维护性
class ChatState {
  // === UI状态 ===
  // 是否正在显示AI回复状态
  final RxBool isAiReplying = false.obs;
  // 是否正在加载更多历史消息
  final RxBool isLoadingHistory = false.obs;
  // 是否正在加载更多消息
  final RxBool isLoadingMore = false.obs;
  
  // === 业务状态 ===
  // 当前活跃的会话ID
  final RxInt activeConversationId = RxInt(0);
  // 当前活跃的AI角色ID
  final RxInt activeAiRoleId = RxInt(0);
  // WebSocket连接状态
  final RxBool isConnected = RxBool(false);
  // 是否有更多消息可加载
  final RxBool hasMoreMessages = true.obs;
  // 当前页码，用于分页加载历史消息
  final RxInt currentPage = RxInt(1);
  
  // === 消息列表 ===
  final RxList<Message> messages = <Message>[].obs;
  
  // === 角色信息 ===
  // 当前角色信息
  final Rx<AiRole?> currentRole = Rx<AiRole?>(null);
  // 是否正在加载角色信息
  final RxBool isLoadingRoleInfo = false.obs;

  /// 重置状态
  void reset() {
    messages.clear();
    currentPage.value = 1;
    hasMoreMessages.value = true;
    isLoadingHistory.value = false;
    isLoadingMore.value = false;
    // 不重置角色信息，保留currentRole.value
  }
  
  /// 设置活跃会话
  void setActiveSession(int conversationId, int aiRoleId) {
    activeConversationId.value = conversationId;
    activeAiRoleId.value = aiRoleId;
  }
  

  
  /// 更新消息状态
  /// 
  /// [messageId] 消息ID
  /// [status] 新的消息状态
  void updateMessageStatus(String messageId, MessageStatus status) {
    final messageIndex = messages.indexWhere((m) => m.id == messageId);
    if (messageIndex != -1) {
      messages[messageIndex] = messages[messageIndex].copyWith(status: status);
      messages.refresh();
    }
  }
  
  /// 更新当前角色信息
  /// 
  /// [role] 新的角色信息
  void updateCurrentRole(AiRole? role) {
    currentRole.value = role;
  }
}

/// 聊天服务
///
/// 作为协调器，整合ChatManager和MessageService的功能，实现IChatService接口
/// 并直接使用WsManager进行通信，实现单WebSocket连接多通道架构
class ChatService extends GetxService implements IChatService {
  // === 依赖服务 ===
  final GlobalState _globalState;
  final ChatManager _chatManager;
  final MessageService _messageService;
  final ChatRepository _repository;
  final WsManager _wsManager = Get.find<WsManager>();
  // EventBus已迁移到GetX响应式机制，不再需要
  
  // === 状态管理 ===
  final ChatState _state = ChatState();
  
  // === 缓存管理 ===
  late final CacheManager _cacheManager;

  // === 配置参数 ===
  final int pageSize = 20;
  
  // === 事件订阅管理 ===
  final List<Worker> _workers = [];

  // === 会话更新相关 ===
  // 会话提供者，用于更新会话列表
  final ISessionProvider? _sessionProvider;
  
  // === 构造函数 ===
  ChatService({
    required GlobalState globalState,
    required ChatManager chatManager,
    required MessageService messageService,
    required ChatRepository repository,
    ISessionProvider? sessionProvider,
  }) : _globalState = globalState,
       _chatManager = chatManager,
       _messageService = messageService,
       _repository = repository,
       _sessionProvider = sessionProvider;
  
  // 用于跟踪是否已设置事件监听器
  bool _eventListenersSetup = false;

  @override
  void onInit() {
    super.onInit();
    
    // 初始化缓存管理器
    _cacheManager = CacheManager.getInstance();
    
    // 设置事件监听器
    _setupEventListeners();
  }
  
  @override
  void onClose() {
    // 取消所有事件订阅
    _cancelAllSubscriptions();
    super.onClose();
  }
  
  /// 取消所有事件订阅
  void _cancelAllSubscriptions() {
    for (var worker in _workers) {
      worker.dispose();
    }
    _workers.clear();
    _eventListenersSetup = false;
    LogUtil.debug('已取消所有ChatService事件订阅');
  }
  
  /// 设置事件监听器
  void _setupEventListeners() {
    // 防止重复设置
    if (_eventListenersSetup) {
      // 先取消之前的所有订阅
      _cancelAllSubscriptions();
    }
    
    LogUtil.debug('设置ChatService事件监听器');

    // 移除roleMessage事件监听器，消息处理已简化为直接调用
    

    // 监听WebSocket连接断开事件，重置AI回复状态
    _workers.add(ever(GlobalEventState.to.websocketDisconnected, (bool disconnected) {
      if (disconnected) {
        LogUtil.debug('ChatService收到WebSocket断开事件，重置AI回复状态');
        _state.isAiReplying.value = false;
      }
    }));
    
    // 监听AI回复状态重置事件
    _workers.add(ever(GlobalEventState.to.aiReplyReset, (Map<String, dynamic>? data) {
      if (data != null) {
        // 获取事件中的角色ID
        final roleId = data['roleId'] as int?;

        LogUtil.debug('ChatService收到AI回复状态重置事件, roleId:$roleId');

        if (roleId != null && roleId > 0) {
          // 如果指定了角色ID，只重置该角色的回复状态
          if (roleId == _chatManager.activeAiRoleId.value) {
            // 如果是当前活跃角色，则重置主AI回复状态
            _state.isAiReplying.value = false;
            LogUtil.debug('重置当前活跃角色(roleId:$roleId)的AI回复状态');
          } else {
            // 如果不是当前活跃角色，则不改变主AI回复状态
            LogUtil.debug('收到非活跃角色(roleId:$roleId)的AI回复状态重置事件，不影响当前活跃角色状态');
          }
        } else {
          // 如果没有指定角色ID，则重置全局AI回复状态（向后兼容）
          _state.isAiReplying.value = false;
          LogUtil.debug('收到全局AI回复状态重置事件，重置所有角色状态');
        }
      }
    }));
    
    _eventListenersSetup = true;
    LogUtil.debug('ChatService事件监听器设置完成');
  }
  
  /// 处理活跃角色的消息
  /// 
  /// [roleId] 角色ID
  /// [wsMsg] WebSocket消息对象
  void _processActiveRoleMessage(int roleId, WsMsg wsMsg) {
    try {
      LogUtil.debug('处理活跃角色消息: roleId=$roleId, event=${wsMsg.event}');
      
      // 确保数据中包含事件类型
      final messageData = Map<String, dynamic>.from(wsMsg.data);
      messageData['event'] = wsMsg.event.toString();
      
      // 提取会话ID
      final conversationId = wsMsg.channelId ?? _chatManager.activeConversationId.value;
      final useRoleId = roleId;
      
      // 提取并处理会话信息
      _extractAndProcessSessionInfo(messageData, wsMsg, conversationId, useRoleId);
      
      // 交由MessageService处理消息
      _messageService.processAiMessage(messageData);
    } catch (e) {
      LogUtil.error('处理活跃角色消息失败: $e');
    }
  }

  /// 统一的WebSocket消息处理方法
  /// 
  /// [wsMsg] WebSocket消息对象
  /// [useRoleId] 使用的角色ID
  /// [conversationId] 会话ID
  /// [messageType] 消息类型描述（用于日志）
  void _processWebSocketMessage(WsMsg wsMsg, int useRoleId, int conversationId, String messageType) {
    LogUtil.debug('收到${messageType}事件: ${wsMsg.event}, 通道: ${wsMsg.channelId}, 数据: ${wsMsg.data}');
    
    // 确保数据中包含事件类型
    final messageData = Map<String, dynamic>.from(wsMsg.data);
    messageData['event'] = wsMsg.event.toString();
    
    // 提取并处理会话信息
    _extractAndProcessSessionInfo(messageData, wsMsg, conversationId, useRoleId);
    
    // 交由MessageService处理消息
    _messageService.processAiMessage(messageData);
  }
  
  /// 提取并处理会话信息
  /// 
  /// [messageData] 消息数据
  /// [wsMsg] WebSocket消息对象
  /// [conversationId] 当前会话ID
  /// [useRoleId] 使用的角色ID
  void _extractAndProcessSessionInfo(Map<String, dynamic> messageData, WsMsg wsMsg, int conversationId, int useRoleId) {
    // 确保消息包含conversation_id
    if (wsMsg.conversationid != null) {
      messageData['conversation_id'] = wsMsg.conversationid;
      LogUtil.debug('从WebSocket消息中提取conversation_id: ${wsMsg.conversationid}');
    } else if (!messageData.containsKey('conversation_id')) {
      // 第一次聊天时可能没有conversation_id，只有在有有效会话ID时才添加
      if (conversationId > 0) {
        messageData['conversation_id'] = conversationId;
        LogUtil.debug('使用当前会话ID作为conversation_id: $conversationId');
      } else {
        LogUtil.debug('新会话消息，暂无conversation_id');
      }
    }
    
    // 确保消息包含role_id
    if (!messageData.containsKey('role_id')) {
      messageData['role_id'] = useRoleId;
      LogUtil.debug('添加role_id到消息数据: $useRoleId');
    }
    
    // 记录最终的会话信息
    final finalConversationId = messageData['conversation_id'];
    final finalRoleId = messageData['role_id'];
    LogUtil.debug('消息最终会话信息: conversation_id=$finalConversationId, role_id=$finalRoleId');
  }

  /// 取消所有未完成的操作
  void _cancelPendingOperations() {
    // 设置AI回复状态为false，确保不会显示正在输入状态
    _messageService.handleAiReplyEnd();
    
    LogUtil.debug('已取消所有未完成的操作');
  }
  
  // === IChatService接口实现 ===
  
  // 消息相关接口 - 委托给MessageService
  @override
  RxList<Message> get messages => _messageService.messages;
  
  @override
  RxBool get isAiReplying => _messageService.isAiReplying;
  
  // 获取特定角色的回复状态
  bool isRoleReplying(int roleId) {
    return _messageService.isRoleReplying(roleId);
  }
  
  // 获取角色回复状态Map
  RxMap<int, bool> get aiRoleReplyingMap => _messageService.aiRoleReplyingMap;
  
  @override
  bool get isLoadingHistory => _messageService.isLoadingHistory.value;
  
  @override
  RxBool get isLoadingMore => _messageService.isLoadingMore;
  
  @override
  bool get hasMoreMessages => _messageService.hasMoreMessages.value;
  
  @override
  bool get aiReplyingState => _messageService.isAiReplying.value;
  
  @override
  set aiReplyingState(bool value) {
    _messageService.isAiReplying.value = value;
  }
  
  @override
  void handleAiReplyStart() {
    _messageService.handleAiReplyStart();
  }
  
  @override
  void handleAiReplyEnd() {
    _messageService.handleAiReplyEnd();
  }
  
  @override
  Future<void> sendMessage(String content) async {
    await _messageService.sendMessage(content);
  }
  
  @override
  void addSystemMessage(String content) {
    _messageService.addSystemMessage(content);
  }
  
  @override
  Future<void> addRoleIntroMessage(String intro, {bool forceAdd = false}) async {
    await _messageService.addRoleIntroMessage(intro, forceAdd: forceAdd);
  }
  
  /// 添加AI问候消息
  void addGreetingMessage(String greeting, int aiRoleId) {
    _messageService.addAiGreetingMessage(greeting, aiRoleId);
  }
  
  /// 清除系统消息
  void clearSystemMessage(String content) {
    try {
      // 查找并移除具有特定内容的系统消息
      final messagesToRemove = _messageService.messages
          .where((m) => m.messageType == MessageType.system && m.lastMessage == content)
          .toList();
      
      if (messagesToRemove.isNotEmpty) {
        _messageService.messages.removeWhere(
          (m) => m.messageType == MessageType.system && m.lastMessage == content
        );
        _messageService.messages.refresh();
        LogUtil.debug('已清除系统消息: $content');
      }
    } catch (e) {
      LogUtil.error('清除系统消息失败: $e');
    }
  }
  
  @override
  Future<bool> sendMessageViaWebSocket(Message message, String content) async {
    return await _messageService.sendMessage(content, 
      conversationId: message.conversationId,
      conversationid: message.conversationid
    );
  }
  
  @override
  Future<void> loadHistoryMessages(int conversationId, {bool resetPage = false, bool forceRefresh = false}) async {
    // 如果conversationId为0，表示没有会话ID，显示greeting
    if (conversationId <= 0) {
      LogUtil.info('会话ID无效(${conversationId})，不加载历史消息，显示问候语');
      
      // 显示当前角色的greeting，但仅在必要时显示
      final currentRole = _chatManager.currentRole.value;
      
      // 确保先清空现有消息，再添加问候语
      _messageService.messages.clear();
      
      if (currentRole != null && currentRole.greeting != null && currentRole.greeting!.isNotEmpty) {
        LogUtil.debug('显示角色greeting: ${currentRole.greeting}');
        
        // 创建一个AI消息来显示greeting
        final aiMessage = Message(
          id: 'greeting_${DateTime.now().millisecondsSinceEpoch}',
          senderUserId: 'ai_${currentRole.id}',
          receiverUserId: 'user_system',
          lastMessage: currentRole.greeting!,
          time: DateTime.now(),
          messageType: MessageType.text,
          conversationId: 0,
          conversationid: _messageService.currentconversationid,
          status: MessageStatus.sent,
        );
        
        // 添加到消息列表
        _messageService.messages.add(aiMessage);
        _messageService.messages.refresh();
      }
      
      // 清除角色绑定
      if (_chatManager.activeAiRoleId.value > 0) {
        final roleId = _chatManager.activeAiRoleId.value;
        
        if (Get.isRegistered<SessionBindingService>()) {
          final bindingService = Get.find<SessionBindingService>();
          bindingService.clearBinding(roleId);
          LogUtil.debug('已清除角色绑定: roleId=$roleId');
        }
        
        // 更新角色对象的conversationId
        try {
          final roleService = Get.find<RoleProvider>();
          await roleService.updateRoleConversationId(roleId, 0);
          LogUtil.debug('已更新角色会话ID为0: roleId=$roleId');
        } catch (e) {
          LogUtil.error('更新角色会话ID失败: $e');
        }
      }
      
      return;
    }
    
    // 如果需要重置页码，立即设置
    if (resetPage) {
      _messageService.currentPage.value = 1;
      LogUtil.debug('重置页码为1');
    }
    
    // 先尝试从缓存读取，除非强制刷新
    final cacheKey = 'chat_history_$conversationId';
    
    if (!forceRefresh) {
      final cachedMessages = await _getCachedMessages(cacheKey);
      
      if (cachedMessages.isNotEmpty && _messageService.messages.isEmpty) {
        LogUtil.debug('从缓存读取到${cachedMessages.length}条聊天记录');
        // 处理缓存的消息
        _messageService.processHistoryMessages(cachedMessages, conversationId, resetPage);
        // 异步刷新最新消息
        _loadAndCacheMessages(conversationId, cacheKey, resetPage: resetPage);
        return;
      }
    } else {
      LogUtil.debug('强制刷新，跳过缓存读取');
    }
    
    // 直接从服务器加载
    await _loadAndCacheMessages(conversationId, cacheKey, resetPage: resetPage, forceRefresh: forceRefresh);
  }
  
  // 加载并缓存消息
  Future<void> _loadAndCacheMessages(int conversationId, String cacheKey, {bool resetPage = false, bool forceRefresh = false}) async {
    try {
      // 保存当前加载的会话ID
      _messageService.currentLoadingConversationId = conversationId;
      
      _messageService.isLoadingHistory.value = true;
      _messageService.isLoadingMore.value = true;
      
      // 确保在重置页面时，页码一定是1
      if (resetPage) {
        _messageService.currentPage.value = 1;
      }
      // 获取当前页码
      final page = resetPage ? 1 : _messageService.currentPage.value;
      
      LogUtil.debug('加载消息，会话ID=$conversationId，页码=$page${forceRefresh ? "，强制刷新=true" : ""}');
      
      // 从仓库加载消息
      final historyMessages = await _repository.getHistoryMessages(conversationId, page, pageSize);
      
      // 检查用户是否已切换到其他会话
      if (_messageService.currentLoadingConversationId != conversationId) {
        LogUtil.warn('用户已切换到其他会话，丢弃已加载的消息。请求会话ID=$conversationId，当前会话ID=${_messageService.currentLoadingConversationId}');
        return; // 直接返回，不更新UI
      }
      
      // 直接通过历史消息判断会话是否存在，不再尝试GET请求会话信息
      if (historyMessages.isEmpty && page == 1) {
        LogUtil.warn('会话ID=$conversationId 没有历史消息，会话不存在或为新会话');
        
        // 如果是第一页且没有消息，清除该会话的缓存
        await _cacheManager.remove(cacheKey);
        LogUtil.debug('已清除会话ID=$conversationId 的缓存');
        
        // 重置消息列表
        if (resetPage) {
          _messageService.messages.clear();
          LogUtil.debug('已清空消息列表');
        }
        
        // 删除重复的问候语代码，改为清除角色绑定
        if (resetPage && _chatManager.activeAiRoleId.value > 0) {
          final roleId = _chatManager.activeAiRoleId.value;
          
          // 清除角色绑定
          if (Get.isRegistered<SessionBindingService>()) {
            final bindingService = Get.find<SessionBindingService>();
            bindingService.clearBinding(roleId);
            LogUtil.debug('已清除角色绑定: roleId=$roleId, conversationId=$conversationId');
          }
          
          // 更新角色对象的conversationId
          try {
            final roleService = Get.find<RoleProvider>();
            await roleService.updateRoleConversationId(roleId, 0);
            LogUtil.debug('已更新角色会话ID为0: roleId=$roleId');
          } catch (e) {
            LogUtil.error('更新角色会话ID失败: $e');
            ErrorHandler.handleException(
              AppException(
                'failed to update role information',
                code: ErrorCodes.ROLE_INFO_LOAD_FAILED,
                originalError: e
              ),
              showSnackbar: false,  // 内部错误，不向用户显示
            );
          }
          
          return;
        }
      }
      
      // 再次检查用户是否已切换到其他会话
      if (_messageService.currentLoadingConversationId != conversationId) {
        LogUtil.warn('处理消息前检测到用户已切换到其他会话，丢弃已加载的消息。请求会话ID=$conversationId，当前会话ID=${_messageService.currentLoadingConversationId}');
        return; // 直接返回，不更新UI
      }
      
      // 处理历史消息
      _messageService.processHistoryMessages(historyMessages, conversationId, resetPage);
      
      
      // 记录日志
      LogUtil.info('已加载${historyMessages.length}条消息，页码=$page，不进行消息缓存');
      
    } catch (e) {
      LogUtil.error('加载消息失败: $e');
      
      // 使用统一的ErrorHandler处理异常
      ErrorHandler.handleException(
        AppException(
          'failed to load history messages',
          code: ErrorCodes.CHAT_HISTORY_LOAD_FAILED,
          originalError: e
        ),
        showSnackbar: false, // 不显示Snackbar，因为我们已经有自己的系统消息
      );
      
      // 如果是第一页加载失败，添加系统消息
      if (resetPage && _messageService.messages.isEmpty) {
        _messageService.addSystemMessage('failed to load history messages');
      }
    } finally {
      _messageService.isLoadingHistory.value = false;
      _messageService.isLoadingMore.value = false;
    }
  }

  // 从缓存获取消息历史
  Future<List<Message>> _getCachedMessages(String cacheKey) async {
    try {
      // 不使用持久化缓存，只使用内存缓存
      final cachedData = await _cacheManager.get<Map<String, dynamic>>(
        cacheKey,
        strategy: CacheStrategy.memoryOnly, // 从CacheStrategy.memoryThenPersistent改为CacheStrategy.memoryOnly
      );
      
      if (cachedData != null && cachedData.containsKey('messages')) {
        final messagesList = cachedData['messages'] as List;
        
        // 在从缓存恢复消息之前，检查并预处理时间字段
        final processedMessages = messagesList.map((m) {
          final messageMap = m as Map<String, dynamic>;
          
          // 检查时间字段是否是字符串，如果是，尝试预处理
          if (messageMap.containsKey('time') && messageMap['time'] is String) {
            final timeStr = messageMap['time'] as String;
            
            try {
              // 尝试直接解析时间字符串
              final dateTime = DateTime.parse(timeStr.replaceAll(" ", "T"));
              // 替换为DateTime对象
              messageMap['time'] = dateTime;
            } catch (e) {
              LogUtil.warn('缓存时间字符串解析失败: $e，保持原始值');
              // 保持原始值，让Message._parseDateTime处理
            }
          }
          
          return messageMap;
        }).toList();
        
        // 使用处理后的消息列表创建Message对象
        return processedMessages.map((m) => Message.fromJson(m)).toList();
      }
      
      return [];
    } catch (e) {
      LogUtil.error('读取缓存消息失败: $e');
      return [];
    }
  }
  
  @override
  Future<void> loadMoreHistoryMessages(int conversationId) async {
    final cacheKey = 'chat_history_$conversationId';
    await _loadAndCacheMessages(conversationId, cacheKey, resetPage: false);
  }
  
  @override
  void sendTypingStatus(bool isTyping) {
    _messageService.sendTypingStatus(isTyping);
  }
  
  @override
  void updateSessionsList() {
    _messageService.updateSessionsList();
  }
  
  // 会话相关接口 - 委托给ChatManager
  @override
  int get currentConversationId => _chatManager.activeConversationId.value;
  
  @override
  int get currentAiRoleId => _chatManager.activeAiRoleId.value;
  
  @override
  bool get isConnected => _wsManager.isConnected;
  
  @override
  Rx<AiRole?> get currentRole => _chatManager.currentRole;
  
  @override
  RxBool get isLoadingRoleInfo => _chatManager.isLoadingRoleInfo;
  
  @override
  Future<void> switchConversation(int conversationId, int aiRoleId) async {
    LogUtil.debug('开始切换会话: conversationId=$conversationId, aiRoleId=$aiRoleId');
    
    // 取消所有未完成的操作
    _cancelPendingOperations();
    LogUtil.debug('已取消所有未完成的操作');
    
    // 立即更新当前加载会话ID，确保之前的加载请求不会影响新会话
    _messageService.currentLoadingConversationId = conversationId;
    LogUtil.debug('已更新当前加载会话ID: ${_messageService.currentLoadingConversationId}');
    
    // 记录角色切换信息，但不清除前一个角色的待处理消息
    final previousRoleId = _chatManager.activeAiRoleId.value;
    if (previousRoleId > 0 && previousRoleId != aiRoleId) {
      LogUtil.info('切换角色：从角色ID=$previousRoleId 切换到 角色ID=$aiRoleId，保留各角色的待处理消息');
    }
    
    // 保存当前用户发送但尚未收到回复的消息
    List<Message> pendingMessages = [];
    
    // 获取当前角色在全局待处理消息系统中的所有待处理消息ID
    final List<String> pendingMessageIds = _messageService.getPendingMessageIdsByRoleId(aiRoleId);
    
    if (pendingMessageIds.isNotEmpty) {
      LogUtil.info('从全局待处理系统中找到${pendingMessageIds.length}条与角色${aiRoleId}相关的待处理消息ID: ${pendingMessageIds.join(", ")}');
    } else {
      LogUtil.info('在全局待处理系统中未找到与角色${aiRoleId}相关的待处理消息');
    }
    
    // 从当前UI中查找所有发送给当前角色的待处理消息
    final String targetReceiverUserId = 'ai_$aiRoleId';
    for (final msg in _messageService.messages) {
      // 只保存发送给目标角色的用户消息
      if (msg.senderUserId == _globalState.currentUser.value?.uid && 
          msg.receiverUserId == targetReceiverUserId &&
          (msg.status == MessageStatus.sending || msg.status == MessageStatus.sent) && 
          DateTime.now().difference(msg.time).inMinutes < 2) { // 只保存最近2分钟内的消息
        pendingMessages.add(msg);
        LogUtil.info('已从当前消息列表中保存发给角色${aiRoleId}的待处理消息: ID=${msg.id}, 内容: ${msg.lastMessage.substring(0, math.min(20, msg.lastMessage.length))}');
      }
    }
    
    if (pendingMessages.isEmpty) {
      LogUtil.info('在当前UI消息列表中未找到发给角色${aiRoleId}的待处理消息');
    } else {
      LogUtil.info('从当前UI消息列表中共找到${pendingMessages.length}条发给角色${aiRoleId}的待处理消息');
    }
    
    // 使用info级别日志，确保在控制台可见
    if (pendingMessages.isNotEmpty) {
      LogUtil.info('已保存${pendingMessages.length}条待处理消息，消息内容: ${pendingMessages.map((m) => m.lastMessage.substring(0, math.min(10, m.lastMessage.length))).join(", ")}');
    } else {
      LogUtil.info('未发现待处理消息需要保存');
      
      // 检查当前消息列表
      if (_messageService.messages.isNotEmpty) {
        final userMessages = _messageService.messages.where((m) => m.senderUserId == _globalState.currentUser.value?.uid).toList();
        if (userMessages.isNotEmpty) {
          LogUtil.info('当前有${userMessages.length}条用户消息，但未满足保存条件');
          LogUtil.info('用户消息状态: ${userMessages.map((m) => '${m.status}').join(", ")}');
        } else {
          LogUtil.info('当前消息列表中没有用户消息');
        }
      } else {
        LogUtil.info('当前消息列表为空');
      }
    }
    
    // 重置消息状态
    _messageService.reset();
    
    // 显式重置页码
    _messageService.currentPage.value = 1;
    
    // 添加日志，帮助诊断问题
    LogUtil.info('切换会话: 正在从会话ID=${_chatManager.activeConversationId.value}切换到会话ID=$conversationId');
    
    try {
      // 只有在conversationId为0时才生成新的会话ID
      if (conversationId == 0) {
        _messageService.generateNewconversationid();
        LogUtil.debug('为新会话生成临时ID');
      }
      
      // 检查WebSocket连接状态
      if (!_wsManager.isConnected) {
        LogUtil.warn('WebSocket未连接，尝试重新连接...');
        
        // 尝试重新连接WebSocket
        bool reconnected = false;
        try {
          reconnected = await _wsManager.reconnect();
          if (!reconnected) {
            LogUtil.error('WebSocket重连失败');
            // 添加系统消息提示用户，但继续流程
            _messageService.addSystemMessage('failed to connect to the server, please check the network and try again');
          } else {
            LogUtil.debug('WebSocket重连成功');
          }
        } catch (e) {
          LogUtil.error('WebSocket重连过程中出错: $e');
          // 添加系统消息提示用户，但继续流程
          _messageService.addSystemMessage('connection error, please try again later');
          reconnected = false;
        }
        
        // 无论WebSocket连接是否成功，都继续后续流程
        LogUtil.debug('WebSocket连接状态: ${reconnected ? "已连接" : "未连接"}，继续切换角色流程');
      }
      
      // 切换会话 - 由ChatManager负责处理WebSocket连接
      await _chatManager.switchConversation(conversationId, aiRoleId);
      
      // 确保角色在AiChannelManager中被订阅
      if (Get.isRegistered<AiChannelManager>()) {
        final channelManager = Get.find<AiChannelManager>();
        channelManager.setActiveRole(aiRoleId);
        LogUtil.debug('已设置活跃角色: $aiRoleId');
      }
      
      // 只有在WebSocket连接成功时才进行通道切换操作
      if (_wsManager.isConnected) {
        // 如果conversationId为0，表示没有会话ID，暂不切换通道
        if (conversationId > 0) {
          // 切换到会话通道 - 用于发送消息
          _wsManager.switchChannel(conversationId);
          LogUtil.debug('已切换到会话通道 $conversationId');
        } else {
          // 切换到全局通道
          _wsManager.switchToGlobalChannel();
          LogUtil.debug('已切换到全局通道，等待新会话ID分配');
        }
      } else {
        LogUtil.debug('WebSocket未连接，跳过通道切换');
      }
      
      // 清空消息列表，确保不会显示上一个会话的消息
      _messageService.messages.clear();
      LogUtil.debug('已清空消息列表');
      
      // 清除当前会话的缓存，确保使用最新的HTTP数据
      if (_chatManager.activeConversationId.value > 0 && _chatManager.activeConversationId.value != conversationId) {
        final oldCacheKey = 'chat_history_${_chatManager.activeConversationId.value}';
        await _cacheManager.remove(oldCacheKey);
        LogUtil.debug('已清除旧会话ID=${_chatManager.activeConversationId.value}的缓存');
      }
      
      // 如果是切换到新会话，也预先清除目标会话的缓存
      if (conversationId > 0) {
        final newCacheKey = 'chat_history_$conversationId';
        await _cacheManager.remove(newCacheKey);
        LogUtil.debug('已清除目标会话ID=$conversationId的缓存');
      }
      
      LogUtil.debug('开始加载历史消息，会话ID=$conversationId');
      
      // 加载历史消息 - 明确指定resetPage=true和forceRefresh=true，确保每次切换会话时都从服务器加载最新数据
      // 我们将直接通过消息历史记录来判断会话是否存在，不再尝试GET请求获取会话信息
      if (conversationId > 0) {
        // 始终强制从服务器加载最新的历史消息，避免使用缓存
        await loadHistoryMessages(conversationId, resetPage: true, forceRefresh: true);
      } else {
        // 如果conversationId为0，表示需要创建新会话，直接加载greeting
        await loadHistoryMessages(0, resetPage: true);
      }
      
      // 只在新会话（conversationId为0）或消息列表为空时才添加角色简介
      if (conversationId == 0 || _messageService.messages.isEmpty) {
        // 获取当前角色信息
        final currentRole = _chatManager.currentRole.value;
        if (currentRole != null && currentRole.description.isNotEmpty) {
          // 添加角色简介系统消息
          await _messageService.addRoleIntroMessage(currentRole.description, forceAdd: true);
          LogUtil.info('已添加角色简介系统消息: ${currentRole.name}');
        } else {
          // 如果没有角色描述，尝试加载角色信息
          final role = await _chatManager.loadRoleInfo(aiRoleId);
          if (role != null && role.description.isNotEmpty) {
            // 添加角色简介系统消息
            await _messageService.addRoleIntroMessage(role.description, forceAdd: true);
            LogUtil.info('已添加角色简介系统消息(从加载的角色信息): ${role.name}');
          } else {
            // 如果仍然没有描述，添加默认简介
            final roleName = role?.name ?? "AI助手";
            await _messageService.addRoleIntroMessage('Intro: A rebellious teenager who loves rock music and often skips school. You are her tutor who tries to help her get back on track.', forceAdd: true);
            LogUtil.info('已添加默认角色简介系统消息: $roleName');
          }
        }
      }
      
      // 第一步：先恢复从当前UI消息列表中找到的待处理消息
      List<Message> allMessagesToShow = [];
      
      if (pendingMessages.isNotEmpty) {
        final existingMessageIds = _messageService.messages.map((m) => m.id).toSet();
        
        for (final message in pendingMessages) {
          // 跳过已存在的消息ID
          if (existingMessageIds.contains(message.id)) {
            LogUtil.info('跳过已存在的消息ID: ${message.id}');
            continue;
          }
          
          // 直接添加原始消息，不修改接收者
          allMessagesToShow.add(message);
          existingMessageIds.add(message.id);
          
          LogUtil.info('从UI消息列表中恢复待处理消息: ID=${message.id}, 接收者=${message.receiverUserId}, 内容: ${message.lastMessage.substring(0, math.min(20, message.lastMessage.length))}');
        }
      }
      
      // 第二步：从全局待处理消息系统中恢复当前角色的待处理消息
      // 这里的pendingMessageIds是之前获取的当前角色所有待处理消息ID
      if (pendingMessageIds.isNotEmpty) {
        LogUtil.info('准备从全局系统恢复${pendingMessageIds.length}条角色${aiRoleId}的待处理消息');
        
        // 使用新方法获取所有角色相关的待处理消息
        List<Message> pendingMessagesFromSystem = _messageService.getPendingMessagesByRoleId(aiRoleId);
        
        if (pendingMessagesFromSystem.isNotEmpty) {
          final existingMessageIds = _messageService.messages.map((m) => m.id).toSet();
          
          for (final message in pendingMessagesFromSystem) {
            if (!existingMessageIds.contains(message.id)) {
              allMessagesToShow.add(message);
              existingMessageIds.add(message.id);
              LogUtil.info('从全局系统恢复待处理消息: ID=${message.id}, 内容: ${message.lastMessage.substring(0, math.min(20, message.lastMessage.length))}');
            } else {
              LogUtil.info('跳过已存在的消息ID: ${message.id}');
            }
          }
          
          LogUtil.info('从全局系统恢复了${pendingMessagesFromSystem.length}条待处理消息');
        } else {
          LogUtil.info('从全局系统恢复待处理消息失败，未找到相关消息内容');
        }
      }
      
      // 添加所有需要显示的消息
      if (allMessagesToShow.isNotEmpty) {
        // 一次性添加所有匹配的消息
        _messageService.messages.addAll(allMessagesToShow);
        _messageService.messages.refresh();
        
        // 保持AI回复状态
        _messageService.isAiReplying.value = true;
        
        LogUtil.info('总共已恢复${allMessagesToShow.length}条匹配当前角色的待处理消息');
      } else {
        LogUtil.info('没有找到需要恢复的待处理消息');
      }
      
    } catch (e) {
      LogUtil.error('切换会话失败: $e');
      
      
      // 使用ErrorHandler处理错误
      ErrorHandler.handleException(
        AppException('failed to switch conversation, please try again', code: ErrorCodes.SESSION_SWITCH_FAILED, originalError: e),
        showSnackbar: true,
      );
    }
  }
  
  @override
  Future<AiRole?> loadRoleInfo(int aiRoleId) async {
    return await _chatManager.loadRoleInfo(aiRoleId);
  }
  
  @override
  Future<AiRole?> switchRole({bool isNext = true, bool fromSessionsList = false, bool fromRecommendList = false}) async {
    try {
      // 获取当前角色ID
      final currentRoleId = _chatManager.activeAiRoleId.value;
      
      // 确定角色来源
      bool useFromSessionsList = fromSessionsList;
      bool useFromRecommendList = fromRecommendList;
      
      // 如果两个标志都为true，优先使用fromSessionsList
      if (useFromSessionsList && useFromRecommendList) {
        LogUtil.warn('同时设置了fromSessionsList和fromRecommendList，优先使用fromSessionsList');
        useFromRecommendList = false;
      }
      
      // 记录日志
      if (useFromSessionsList) {
        LogUtil.info('从会话列表获取${isNext ? "下一个" : "上一个"}角色');
      } else if (useFromRecommendList) {
        LogUtil.info('从推荐列表获取${isNext ? "下一个" : "上一个"}角色');
      } else {
        LogUtil.info('从默认列表获取${isNext ? "下一个" : "上一个"}角色');
      }
      
      // 根据方向获取目标角色
      AiRole? targetRole;
      if (isNext) {
        targetRole = await getNextRole(currentRoleId, useFromSessionsList, useFromRecommendList);
      } else {
        targetRole = await getPreviousRole(currentRoleId, useFromSessionsList, useFromRecommendList);
      }
      
      // 检查是否获取到角色
      if (targetRole == null) {
        LogUtil.warn('无法获取${isNext ? "下一个" : "上一个"}角色信息');
        return null;
      }
      
      // 获取新的会话ID
      int newConversationId = targetRole.conversationId ?? 0;
      LogUtil.debug('switchRole - 角色信息: roleId=${targetRole.id}, name=${targetRole.name}, conversationId=$newConversationId');
      
      // 关键修复：先预设角色信息，确保UI能立即更新
      _chatManager.presetRoleInfo(targetRole);
      
      // 直接调用switchConversation，让它处理所有逻辑
      LogUtil.info('切换到会话: conversationId=$newConversationId, aiRoleId=${targetRole.id}');
      await switchConversation(newConversationId, targetRole.id);
      
      return targetRole;
    } catch (e) {
      LogUtil.error('切换角色失败: $e');
      return null;
    }
  }
  
  @override
  void presetRoleInfo(AiRole role) {
    _chatManager.presetRoleInfo(role);
  }
  
  /// 清空消息列表
  void clearMessages() {
    _messageService.messages.clear();
    LogUtil.debug('已清空消息列表');
  }
  
  /// 获取下一个角色信息（不切换会话）
  Future<AiRole?> getNextRole(int currentRoleId, bool fromSessionsList, [bool fromRecommendList = false]) async {
    try {
      // 使用RoleService获取角色信息
      final roleService = Get.find<RoleProvider>();
      
      // 如果fromRecommendList为true，强制使用推荐列表
      if (fromRecommendList) {
        return await roleService.getNextRecommendRole(currentRoleId);
      }
      
      // 否则传递fromSessionsList参数，决定从哪个列表获取角色
      return await roleService.getNextRole(currentRoleId, fromSessionsList);
    } catch (e) {
      LogUtil.error('获取下一个角色信息失败: $e');
      return null;
    }
  }
  
  /// 获取上一个角色信息（不切换会话）
  Future<AiRole?> getPreviousRole(int currentRoleId, bool fromSessionsList, [bool fromRecommendList = false]) async {
    try {
      // 使用RoleService获取角色信息
      final roleService = Get.find<RoleProvider>();
      
      // 如果fromRecommendList为true，强制使用推荐列表
      if (fromRecommendList) {
        return await roleService.getPreviousRecommendRole(currentRoleId);
      }
      
      // 否则传递fromSessionsList参数，决定从哪个列表获取角色
      return await roleService.getPreviousRole(currentRoleId, fromSessionsList);
    } catch (e) {
      LogUtil.error('获取上一个角色信息失败: $e');
      return null;
    }
  }

  /// 重置会话ID
  /// 
  /// 将当前会话ID设置为0，表示需要创建新会话
  Future<void> resetConversationId() async {
    // 获取当前角色ID和会话ID
    final roleId = _chatManager.activeAiRoleId.value;
    final currentConversationId = _chatManager.activeConversationId.value;
    
    // 检查是否有问候语消息需要保留
    final hasGreetingMessage = _messageService.messages.any((msg) => 
        msg.id.startsWith('greeting_') && msg.conversationId == 0);
    
    // 清除当前会话的缓存
    if (currentConversationId > 0) {
      final cacheKey = 'chat_history_$currentConversationId';
      await _cacheManager.remove(cacheKey);
      LogUtil.debug('已清除会话ID=$currentConversationId的缓存');
    }
    
    // 清除角色绑定
    if (roleId > 0 && Get.isRegistered<SessionBindingService>()) {
      final bindingService = Get.find<SessionBindingService>();
      bindingService.clearBinding(roleId);
      LogUtil.debug('已清除角色绑定: roleId=$roleId');
    }
    
    // 重置会话ID为0
    _chatManager.setActiveSession(0, roleId);
    LogUtil.debug('已重置会话ID为0，表示需要创建新会话');
    
    // 移除清空消息列表的逻辑，让调用者决定是否清空消息列表
  }
}
