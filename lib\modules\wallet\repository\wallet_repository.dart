import 'package:shared_preferences/shared_preferences.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/common/utils/error_handler.dart';
import 'package:rolio/common/constants/error_codes.dart';
import 'package:rolio/modules/wallet/model/wallet.dart';
import 'package:rolio/modules/wallet/model/transaction_record.dart';
import 'dart:convert';
import 'package:uuid/uuid.dart';

/// 钱包Repository接口
abstract class IWalletRepository {
  /// 获取钱包信息
  Future<Wallet?> getWallet();
  
  /// 更新余额
  Future<bool> updateBalance(int newBalance);
  
  /// 添加交易记录
  Future<bool> addTransaction(TransactionRecord record);
  
  /// 获取交易历史
  Future<List<TransactionRecord>> getTransactionHistory({int? limit});
  
  /// 清空交易历史
  Future<bool> clearTransactionHistory();
  
  /// 初始化钱包（首次使用）
  Future<bool> initializeWallet({int initialDiamonds = 1000});
}

/// 本地钱包Repository实现
class LocalWalletRepository implements IWalletRepository {
  // SharedPreferences实例
  SharedPreferences? _prefs;
  
  // UUID生成器
  static const _uuid = Uuid();
  
  // 存储键名常量
  static const String _walletKey = 'wallet_data';
  static const String _transactionsKey = 'wallet_transactions';
  static const String _initializedKey = 'wallet_initialized';
  
  /// 初始化SharedPreferences
  Future<void> _ensureInitialized() async {
    _prefs ??= await SharedPreferences.getInstance();
  }

  @override
  Future<Wallet?> getWallet() async {
    try {
      await _ensureInitialized();
      
      final walletData = _prefs!.getString(_walletKey);
      if (walletData == null) {
        LogUtil.debug('LocalWalletRepository: 钱包数据不存在，返回null');
        return null;
      }
      
      final walletMap = json.decode(walletData) as Map<String, dynamic>;
      final wallet = Wallet.fromMap(walletMap);
      
      LogUtil.debug('LocalWalletRepository: 获取钱包数据成功，余额: ${wallet.diamonds}');
      return wallet;
    } catch (e) {
      LogUtil.error('LocalWalletRepository: 获取钱包数据失败: $e');
      ErrorHandler.handleException(
        AppException('获取钱包数据失败', originalError: e, code: ErrorCodes.BUSINESS_ERROR),
        showSnackbar: false,
      );
      return null;
    }
  }

  @override
  Future<bool> updateBalance(int newBalance) async {
    try {
      await _ensureInitialized();
      
      if (newBalance < 0) {
        LogUtil.warn('LocalWalletRepository: 余额不能为负数: $newBalance');
        return false;
      }
      
      final wallet = Wallet(
        diamonds: newBalance,
        lastUpdated: DateTime.now(),
      );
      
      final walletData = json.encode(wallet.toMap());
      final success = await _prefs!.setString(_walletKey, walletData);
      
      if (success) {
        LogUtil.debug('LocalWalletRepository: 更新余额成功: $newBalance');
      } else {
        LogUtil.error('LocalWalletRepository: 更新余额失败');
      }
      
      return success;
    } catch (e) {
      LogUtil.error('LocalWalletRepository: 更新余额异常: $e');
      ErrorHandler.handleException(
        AppException('更新余额失败', originalError: e, code: ErrorCodes.BUSINESS_ERROR),
        showSnackbar: false,
      );
      return false;
    }
  }

  @override
  Future<bool> addTransaction(TransactionRecord record) async {
    try {
      await _ensureInitialized();
      
      // 获取现有交易记录
      final transactions = await getTransactionHistory();
      
      // 添加新记录到列表开头（最新的在前面）
      transactions.insert(0, record);
      
      // 限制交易记录数量（保留最近1000条）
      if (transactions.length > 1000) {
        transactions.removeRange(1000, transactions.length);
      }
      
      // 保存交易记录
      final transactionsData = json.encode(
        transactions.map((t) => t.toMap()).toList(),
      );
      
      final success = await _prefs!.setString(_transactionsKey, transactionsData);
      
      if (success) {
        LogUtil.debug('LocalWalletRepository: 添加交易记录成功: ${record.id}');
      } else {
        LogUtil.error('LocalWalletRepository: 添加交易记录失败');
      }
      
      return success;
    } catch (e) {
      LogUtil.error('LocalWalletRepository: 添加交易记录异常: $e');
      ErrorHandler.handleException(
        AppException('添加交易记录失败', originalError: e, code: ErrorCodes.BUSINESS_ERROR),
        showSnackbar: false,
      );
      return false;
    }
  }

  @override
  Future<List<TransactionRecord>> getTransactionHistory({int? limit}) async {
    try {
      await _ensureInitialized();
      
      final transactionsData = _prefs!.getString(_transactionsKey);
      if (transactionsData == null) {
        LogUtil.debug('LocalWalletRepository: 交易记录不存在，返回空列表');
        return [];
      }
      
      final transactionsList = json.decode(transactionsData) as List<dynamic>;
      final transactions = transactionsList
          .map((t) => TransactionRecord.fromMap(t as Map<String, dynamic>))
          .toList();
      
      // 按时间倒序排列（最新的在前面）
      transactions.sort((a, b) => b.timestamp.compareTo(a.timestamp));
      
      // 应用限制
      if (limit != null && limit > 0 && transactions.length > limit) {
        return transactions.take(limit).toList();
      }
      
      LogUtil.debug('LocalWalletRepository: 获取交易记录成功，数量: ${transactions.length}');
      return transactions;
    } catch (e) {
      LogUtil.error('LocalWalletRepository: 获取交易记录失败: $e');
      ErrorHandler.handleException(
        AppException('获取交易记录失败', originalError: e, code: ErrorCodes.BUSINESS_ERROR),
        showSnackbar: false,
      );
      return [];
    }
  }

  @override
  Future<bool> clearTransactionHistory() async {
    try {
      await _ensureInitialized();
      
      final success = await _prefs!.remove(_transactionsKey);
      
      if (success) {
        LogUtil.debug('LocalWalletRepository: 清空交易记录成功');
      } else {
        LogUtil.error('LocalWalletRepository: 清空交易记录失败');
      }
      
      return success;
    } catch (e) {
      LogUtil.error('LocalWalletRepository: 清空交易记录异常: $e');
      return false;
    }
  }

  @override
  Future<bool> initializeWallet({int initialDiamonds = 1000}) async {
    try {
      await _ensureInitialized();
      
      // 检查是否已经初始化
      final isInitialized = _prefs!.getBool(_initializedKey) ?? false;
      if (isInitialized) {
        LogUtil.debug('LocalWalletRepository: 钱包已经初始化，跳过');
        return true;
      }
      
      // 创建初始钱包
      final wallet = Wallet(
        diamonds: initialDiamonds,
        lastUpdated: DateTime.now(),
      );
      
      // 保存钱包数据
      final walletData = json.encode(wallet.toMap());
      final walletSuccess = await _prefs!.setString(_walletKey, walletData);
      
      // 创建初始交易记录
      final initialTransaction = TransactionRecord(
        id: _uuid.v4(),
        amount: initialDiamonds,
        type: TransactionType.gift,
        description: 'new user gift',
        timestamp: DateTime.now(),
        balanceAfter: initialDiamonds,
      );
      
      // 保存初始交易记录
      final transactionSuccess = await addTransaction(initialTransaction);
      
      // 标记为已初始化
      final initSuccess = await _prefs!.setBool(_initializedKey, true);
      
      final success = walletSuccess && transactionSuccess && initSuccess;
      
      if (success) {
        LogUtil.info('LocalWalletRepository: 钱包初始化成功，初始余额: $initialDiamonds');
      } else {
        LogUtil.error('LocalWalletRepository: 钱包初始化失败');
      }
      
      return success;
    } catch (e) {
      LogUtil.error('LocalWalletRepository: 钱包初始化异常: $e');
      ErrorHandler.handleException(
        AppException('钱包初始化失败', originalError: e, code: ErrorCodes.BUSINESS_ERROR),
        showSnackbar: false,
      );
      return false;
    }
  }

  /// 生成交易ID
  static String generateTransactionId() {
    return _uuid.v4();
  }
}

/// API钱包Repository实现（预留）
class ApiWalletRepository implements IWalletRepository {
  @override
  Future<Wallet?> getWallet() async {
    // TODO: 实现API调用
    throw UnimplementedError('API钱包Repository尚未实现');
  }

  @override
  Future<bool> updateBalance(int newBalance) async {
    // TODO: 实现API调用
    throw UnimplementedError('API钱包Repository尚未实现');
  }

  @override
  Future<bool> addTransaction(TransactionRecord record) async {
    // TODO: 实现API调用
    throw UnimplementedError('API钱包Repository尚未实现');
  }

  @override
  Future<List<TransactionRecord>> getTransactionHistory({int? limit}) async {
    // TODO: 实现API调用
    throw UnimplementedError('API钱包Repository尚未实现');
  }

  @override
  Future<bool> clearTransactionHistory() async {
    // TODO: 实现API调用
    throw UnimplementedError('API钱包Repository尚未实现');
  }

  @override
  Future<bool> initializeWallet({int initialDiamonds = 1000}) async {
    // TODO: 实现API调用
    throw UnimplementedError('API钱包Repository尚未实现');
  }
}
