import 'dart:async';
import 'package:get/get.dart';
import 'package:rolio/common/cache/cache_manager.dart';
import 'package:rolio/common/constants/cache_constants.dart';
import 'package:rolio/common/constants/string_constants.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/modules/role/repository/search_repository.dart';

/// 搜索状态枚举
enum SearchState {
  idle,         // 空闲状态
  searching,    // 搜索中
  loading,      // 加载中
  completed,    // 完成
  error,        // 错误
}


class SearchStateManager {
  // 缓存管理器
  final CacheManager _cacheManager = Get.find<CacheManager>();
  
  // 对外暴露缓存管理器（用于特殊清理操作）
  CacheManager get cacheManager => _cacheManager;
  
  // 搜索状态
  final Rx<SearchState> _searchState = SearchState.idle.obs;
  
  // 搜索结果缓存 Map<cacheKey, Map<String, dynamic>>
  final RxMap<String, Map<String, dynamic>> _searchResultCache = <String, Map<String, dynamic>>{}.obs;
  
  // 搜索历史本地缓存
  final RxList<SearchRecord> _localSearchHistory = <SearchRecord>[].obs;
  
  // 缓存统计
  int _cacheHits = 0;
  int _cacheMisses = 0;
  
  // 对外暴露的状态访问器
  Rx<SearchState> get searchState => _searchState;
  RxMap<String, Map<String, dynamic>> get searchResultCache => _searchResultCache;
  RxList<SearchRecord> get localSearchHistory => _localSearchHistory;
  
  // 缓存统计访问器
  int get cacheHits => _cacheHits;
  int get cacheMisses => _cacheMisses;
  double get cacheHitRate => (_cacheHits + _cacheMisses) > 0 ? _cacheHits / (_cacheHits + _cacheMisses) : 0.0;
  
  /// 生成搜索结果缓存键
  String _generateSearchResultCacheKey(String keyword, int page, int size) {
    final normalizedKeyword = _normalizeKeyword(keyword);
    return '${CacheConstants.searchResultCachePrefix}${normalizedKeyword}_${page}_$size';
  }
  
  /// 标准化关键词（去除空格、转小写、去重复字符）
  String _normalizeKeyword(String keyword) {
    return keyword.trim().toLowerCase().replaceAll(RegExp(r'\s+'), '_');
  }
  
  /// 获取搜索结果缓存
  /// 
  /// [keyword] 搜索关键词
  /// [page] 页码
  /// [size] 每页大小
  /// 返回缓存的搜索结果，如果没有缓存则返回null
  Future<Map<String, dynamic>?> getSearchResultCache(String keyword, int page, int size) async {
    try {
      final cacheKey = _generateSearchResultCacheKey(keyword, page, size);
      
      // 先检查内存缓存
      if (_searchResultCache.containsKey(cacheKey)) {
        _cacheHits++;
        LogUtil.debug('SearchStateManager: 搜索结果内存缓存命中 - $cacheKey');
        return _searchResultCache[cacheKey];
      }
      
      // 检查持久化缓存
      final cachedResult = await _cacheManager.get<Map<String, dynamic>>(
        cacheKey,
        strategy: CacheStrategy.memoryThenPersistent,
        maxAge: CacheConstants.getExpiryForModule('search', type: 'result'),
        fromJson: (json) => json,
      );
      
      if (cachedResult != null) {
        _cacheHits++;
        // 同步到内存缓存
        _searchResultCache[cacheKey] = cachedResult;
        LogUtil.debug('SearchStateManager: 搜索结果持久化缓存命中 - $cacheKey');
        return cachedResult;
      }
      
      _cacheMisses++;
      LogUtil.debug('SearchStateManager: 搜索结果缓存未命中 - $cacheKey');
      return null;
    } catch (e) {
      _cacheMisses++;
      LogUtil.error('SearchStateManager: 获取搜索结果缓存失败 - $e');
      return null;
    }
  }
  
  /// 设置搜索结果缓存
  /// 
  /// [keyword] 搜索关键词
  /// [page] 页码
  /// [size] 每页大小
  /// [result] 搜索结果
  Future<bool> setSearchResultCache(String keyword, int page, int size, Map<String, dynamic> result) async {
    try {
      final cacheKey = _generateSearchResultCacheKey(keyword, page, size);
      
      // 同时写入内存和持久化缓存
      final success = await _cacheManager.set(
        cacheKey,
        result,
        strategy: CacheStrategy.both,
        expiry: CacheConstants.getExpiryForModule('search', type: 'result'),
        toJson: (data) => data,
      );
      
      if (success) {
        // 同步到本地内存缓存
        _searchResultCache[cacheKey] = result;
        LogUtil.debug('SearchStateManager: 搜索结果缓存设置成功 - $cacheKey');
      }
      
      return success;
    } catch (e) {
      LogUtil.error('SearchStateManager: 设置搜索结果缓存失败 - $e');
      return false;
    }
  }
  
  /// 获取本地搜索历史缓存
  Future<List<SearchRecord>> getLocalSearchHistory() async {
    try {
      // 先返回内存中的历史记录
      if (_localSearchHistory.isNotEmpty) {
        _cacheHits++;
        LogUtil.debug('SearchStateManager: 搜索历史内存缓存命中');
        return _localSearchHistory.toList();
      }
      
      // 从持久化缓存中加载
      final cachedHistory = await _cacheManager.get<List<SearchRecord>>(
        CacheConstants.searchHistoryCacheKey,
        strategy: CacheStrategy.persistentOnly,
        maxAge: CacheConstants.getExpiryForModule('search', type: 'history'),
        fromJson: (json) => (json['history'] as List<dynamic>)
            .map((item) => SearchRecord.fromJson(item as Map<String, dynamic>))
            .toList(),
      );
      
      if (cachedHistory != null) {
        _cacheHits++;
        // 同步到内存缓存
        _localSearchHistory.assignAll(cachedHistory);
        LogUtil.debug('SearchStateManager: 搜索历史持久化缓存命中，加载${cachedHistory.length}条记录');
        return cachedHistory;
      }
      
      _cacheMisses++;
      LogUtil.debug('SearchStateManager: 搜索历史缓存未命中');
      return [];
    } catch (e) {
      _cacheMisses++;
      LogUtil.error('SearchStateManager: 获取搜索历史缓存失败 - $e');
      return [];
    }
  }
  
  /// 设置本地搜索历史缓存
  /// 
  /// [history] 搜索历史列表
  Future<bool> setLocalSearchHistory(List<SearchRecord> history) async {
    try {
      final success = await _cacheManager.set(
        CacheConstants.searchHistoryCacheKey,
        {'history': history.map((record) => record.toJson()).toList()},
        strategy: CacheStrategy.both,
        expiry: CacheConstants.getExpiryForModule('search', type: 'history'),
        toJson: (data) => data,
      );
      
      if (success) {
        // 同步到内存缓存
        _localSearchHistory.assignAll(history);
        LogUtil.debug('SearchStateManager: 搜索历史缓存设置成功，共${history.length}条记录');
      }
      
      return success;
    } catch (e) {
      LogUtil.error('SearchStateManager: 设置搜索历史缓存失败 - $e');
      return false;
    }
  }
  
  /// 添加搜索记录到本地历史
  /// 
  /// [keyword] 搜索关键词
  Future<void> addSearchRecord(String keyword) async {
    try {
      if (keyword.trim().isEmpty) return;
      
      final now = DateTime.now();
      final normalizedKeyword = keyword.trim();
      
      // 移除重复的搜索记录
      _localSearchHistory.removeWhere((record) => record.keyword == normalizedKeyword);
      
      // 添加新记录到开头
      final newRecord = SearchRecord(
        id: now.millisecondsSinceEpoch,
        keyword: normalizedKeyword,
        searchCount: 1,
        lastSearchAt: now,
      );
      
      _localSearchHistory.insert(0, newRecord);
      
      // 限制历史记录数量（最多保留50条）
      if (_localSearchHistory.length > 50) {
        _localSearchHistory.removeRange(50, _localSearchHistory.length);
      }
      
      // 异步保存到持久化缓存
      await setLocalSearchHistory(_localSearchHistory.toList());
      
      LogUtil.debug('SearchStateManager: 添加搜索记录 - $normalizedKeyword');
    } catch (e) {
      LogUtil.error('SearchStateManager: 添加搜索记录失败 - $e');
    }
  }
  
  /// 清除搜索缓存
  /// 
  /// [type] 缓存类型：'result', 'suggestions', 'history', 'all'
  Future<bool> clearCache(String type) async {
    try {
      bool success = true;
      
      switch (type) {
        case 'result':
          // 清除搜索结果缓存
          for (final key in _searchResultCache.keys.toList()) {
            await _cacheManager.remove(key);
          }
          _searchResultCache.clear();
          LogUtil.debug('SearchStateManager: 搜索结果缓存已清除');
          break;
          
        case 'suggestions':
          // 搜索建议缓存已移至Repository层，无需处理
          LogUtil.debug('SearchStateManager: 搜索建议缓存已移至Repository层，无需清除');
          break;
          
        case 'history':
          // 清除搜索历史缓存
          success = await _cacheManager.remove(CacheConstants.searchHistoryCacheKey);
          _localSearchHistory.clear();
          LogUtil.debug('SearchStateManager: 搜索历史缓存已清除');
          break;
          
        case 'all':
          // 清除所有搜索相关缓存
          success = await clearCache('result') && 
                   await clearCache('suggestions') && 
                   await clearCache('history');
          LogUtil.debug('SearchStateManager: 所有搜索缓存已清除');
          break;
          
        default:
          LogUtil.warn('SearchStateManager: 未知的缓存类型 - $type');
          success = false;
      }
      
      return success;
    } catch (e) {
      LogUtil.error('SearchStateManager: 清除搜索缓存失败 - $e');
      return false;
    }
  }
  
  /// 设置搜索状态
  void setSearchState(SearchState state) {
    _searchState.value = state;
    LogUtil.debug('SearchStateManager: 搜索状态变更为 - ${state.toString()}');
  }
  
  /// 重置状态管理器
  void reset() {
    _searchState.value = SearchState.idle;
    _searchResultCache.clear();
    _localSearchHistory.clear();
    _cacheHits = 0;
    _cacheMisses = 0;
    LogUtil.debug('SearchStateManager: 状态管理器已重置');
  }
  
  /// 获取缓存统计信息
  Map<String, dynamic> getCacheStats() {
    return {
      'cacheHits': _cacheHits,
      'cacheMisses': _cacheMisses,
      'hitRate': cacheHitRate,
      'resultCacheSize': _searchResultCache.length,
      'historySize': _localSearchHistory.length,
    };
  }
  

  

  
  /// 清理过期缓存
  /// 
  /// 定期清理过期的缓存项，释放内存
  Future<void> cleanupExpiredCache() async {
    try {
      LogUtil.debug('SearchStateManager: 开始清理过期缓存');
      
      int cleanedCount = 0;
      
      // 清理搜索结果缓存中的过期项
      final resultKeysToRemove = <String>[];
      for (final key in _searchResultCache.keys) {
        final expiryTime = await _cacheManager.getExpiryTime(key);
        if (expiryTime != null && DateTime.now().millisecondsSinceEpoch > expiryTime) {
          resultKeysToRemove.add(key);
        }
      }
      
      for (final key in resultKeysToRemove) {
        _searchResultCache.remove(key);
        await _cacheManager.remove(key);
        cleanedCount++;
      }
      
      LogUtil.debug('SearchStateManager: 过期缓存清理完成，清理了$cleanedCount个缓存项');
    } catch (e) {
      LogUtil.error('SearchStateManager: 清理过期缓存失败 - $e');
    }
  }
  
  /// 优化缓存大小
  /// 
  /// 当缓存项过多时，使用LRU策略清理最少使用的缓存
  Future<void> optimizeCacheSize() async {
    try {
      const maxCacheSize = StringsConsts.maxMemCacheSize; // 使用统一的缓存大小配置
      
      // 优化搜索结果缓存
      if (_searchResultCache.length > maxCacheSize) {
        final excessCount = _searchResultCache.length - maxCacheSize;
        final keysToRemove = _searchResultCache.keys.take(excessCount).toList();
        
        for (final key in keysToRemove) {
          _searchResultCache.remove(key);
          await _cacheManager.remove(key);
        }
        
        LogUtil.debug('SearchStateManager: 搜索结果缓存优化完成，移除了$excessCount个缓存项');
      }
    } catch (e) {
      LogUtil.error('SearchStateManager: 优化缓存大小失败 - $e');
    }
  }
}